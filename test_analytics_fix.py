#!/usr/bin/env python
"""
Test script to verify the analytics dashboard fix
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ElDawliya_sys.settings')
django.setup()

def test_analytics_view():
    """Test the analytics dashboard view"""
    try:
        from Hr.views.analytics_views import analytics_dashboard
        from django.test import RequestFactory
        from django.contrib.auth.models import AnonymousUser
        from accounts.models import Users_Login_New
        
        # Create a test request
        factory = RequestFactory()
        request = factory.get('/Hr/analytics/')
        
        # Create a test user (or use anonymous for now)
        request.user = AnonymousUser()
        
        print("Testing analytics dashboard view...")
        
        # Try to call the view
        response = analytics_dashboard(request)
        
        print(f"✅ Analytics dashboard view executed successfully!")
        print(f"Response status: {response.status_code if hasattr(response, 'status_code') else 'N/A'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in analytics dashboard view: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        return False

def test_payroll_query():
    """Test the PayrollEntry query that was causing the FieldError"""
    try:
        from Hr.models.salary_models import PayrollEntry
        from django.db.models import Sum, Avg, Count
        
        print("Testing PayrollEntry query...")
        
        # Test the corrected query
        salary_stats = PayrollEntry.objects.values('period__period').annotate(
            total=Sum('total_amount'),
            avg=Avg('total_amount'),
            count=Count('id')
        ).order_by('-period__period')[:5]
        
        print(f"✅ PayrollEntry query executed successfully!")
        print(f"Number of salary stats records: {len(list(salary_stats))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in PayrollEntry query: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Testing Analytics Dashboard Fix")
    print("=" * 50)
    
    # Test the PayrollEntry query first
    query_success = test_payroll_query()
    
    print("\n" + "-" * 30)
    
    # Test the analytics view
    view_success = test_analytics_view()
    
    print("\n" + "=" * 50)
    if query_success and view_success:
        print("🎉 All tests passed! The fix should work.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    print("=" * 50)
