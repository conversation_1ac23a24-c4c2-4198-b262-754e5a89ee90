{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load form_utils %}
{% load django_permissions %}

{% block title %}إضافة موظف{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="form-container">
  <h2 class="form-title">إضافة موظف جديد</h2>
  <form method="post" autocomplete="off">
    {% csrf_token %}
    <div class="form-fields">
      {{ form.as_p }}
    </div>
    <div class="form-actions">
      <button type="submit" class="modern-btn primary">حفظ</button>
      <a href="{% url 'Hr:employees:list' %}" class="modern-btn secondary">إلغاء</a>
    </div>
  </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحقق من صحة النماذج
    (function () {
        'use strict'

        // أشكال تحتاج إلى التحقق من صحتها
        var forms = document.querySelectorAll('.needs-validation')

        // حلقة عليهم ومنع الإرسال
        Array.prototype.slice.call(forms)
            .forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }

                    form.classList.add('was-validated')
                }, false)
            })
    })()

    // تعبئة الاسم الكامل تلقائيًا
    const firstNameInput = document.getElementById('id_emp_first_name');
    const secondNameInput = document.getElementById('id_emp_second_name');
    const fullNameInput = document.getElementById('id_emp_full_name');

    if (firstNameInput && secondNameInput && fullNameInput) {
        const updateFullName = () => {
            const firstName = firstNameInput.value.trim() || '';
            const secondName = secondNameInput.value.trim() || '';

            if (firstName || secondName) {
                fullNameInput.value = [firstName, secondName].filter(Boolean).join(' ');
            }
        };

        firstNameInput.addEventListener('blur', updateFullName);
        secondNameInput.addEventListener('blur', updateFullName);
    }

    // حساب مبلغ التأمين المستحق
    const insuranceSalaryInput = document.getElementById('id_insurance_salary');
    const percentageInput = document.getElementById('id_percentage_insurance_payable');
    const dueAmountInput = document.getElementById('id_due_insurance_amount');

    if (insuranceSalaryInput && percentageInput && dueAmountInput) {
        const updateDueAmount = () => {
            const salary = parseFloat(insuranceSalaryInput.value) || 0;
            const percentage = parseFloat(percentageInput.value) || 0;

            if (salary && percentage) {
                dueAmountInput.value = (salary * percentage / 100).toFixed(2);
            } else {
                dueAmountInput.value = '';
            }
        };

        insuranceSalaryInput.addEventListener('input', updateDueAmount);
        percentageInput.addEventListener('input', updateDueAmount);
    }
});
</script>
{% endblock %}
