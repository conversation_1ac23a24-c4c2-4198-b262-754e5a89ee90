/*
 * ElDaw<PERSON>ya HR - Modern Design System v2.0
 * Complete redesign with dual theme support
 * Professional, clean, and responsive interface
 */

/* ===== DESIGN TOKENS ===== */
:root {
  /* Primary Brand Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Semantic Colors */
  --success-50: #ecfdf5;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;

  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;

  --danger-50: #fef2f2;
  --danger-500: #ef4444;
  --danger-600: #dc2626;
  --danger-700: #b91c1c;

  --info-50: #f0f9ff;
  --info-500: #06b6d4;
  --info-600: #0891b2;
  --info-700: #0e7490;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Light Theme Variables */
  --primary: var(--primary-600);
  --primary-hover: var(--primary-700);
  --primary-light: var(--primary-100);
  --secondary: var(--gray-600);
  --accent: #f59e0b;
  --success: var(--success-500);
  --warning: var(--warning-500);
  --danger: var(--danger-500);
  --info: var(--info-500);

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-card: #ffffff;
  --bg-sidebar: var(--gray-900);
  --bg-navbar: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-tertiary: var(--gray-500);
  --text-inverse: #ffffff;
  --text-muted: var(--gray-400);

  /* Border & Effects */
  --border-light: var(--gray-200);
  --border-medium: var(--gray-300);
  --border-dark: var(--gray-400);
  --border-radius-sm: 0.375rem;
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Typography */
  --font-family-primary: 'Cairo', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --font-family-secondary: 'Tajawal', 'Inter', 'system-ui', sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;

  /* Enhanced Spacing Scale */
  --space-1: 0.25rem;     /* 4px */
  --space-2: 0.5rem;      /* 8px */
  --space-3: 0.75rem;     /* 12px */
  --space-4: 1rem;        /* 16px */
  --space-5: 1.25rem;     /* 20px */
  --space-6: 1.5rem;      /* 24px */
  --space-8: 2rem;        /* 32px */
  --space-10: 2.5rem;     /* 40px */
  --space-12: 3rem;       /* 48px */
  --space-16: 4rem;       /* 64px */
  --space-20: 5rem;       /* 80px */
  --space-24: 6rem;       /* 96px */

  /* Component-specific spacing for better visual hierarchy */
  --page-padding: var(--space-6);
  --section-gap: var(--space-12);
  --card-padding: var(--space-6);
  --card-gap: var(--space-4);
  --element-gap: var(--space-4);
  --tight-gap: var(--space-2);
  --loose-gap: var(--space-8);
  --content-max-width: 1400px;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  /* Background Colors */
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --bg-card: var(--gray-800);
  --bg-sidebar: var(--gray-900);
  --bg-navbar: var(--gray-800);
  --bg-overlay: rgba(0, 0, 0, 0.7);

  /* Text Colors */
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --text-inverse: var(--gray-900);
  --text-muted: var(--gray-500);

  /* Border Colors */
  --border-light: var(--gray-700);
  --border-medium: var(--gray-600);
  --border-dark: var(--gray-500);

  /* Adjusted Colors for Dark Mode */
  --primary: var(--primary-500);
  --primary-hover: var(--primary-400);
  --primary-light: var(--primary-900);
}
/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  background: var(--bg-secondary);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  transition: background-color var(--transition-normal), color var(--transition-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== LAYOUT COMPONENTS ===== */

/* Application Container */
.app-container {
  display: flex;
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background: var(--bg-sidebar);
  color: var(--text-inverse);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
  transition: transform var(--transition-normal);
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand-icon {
  width: 40px;
  height: 40px;
  background: var(--primary);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--text-inverse);
}

.sidebar-brand-text {
  flex: 1;
}

.sidebar-brand-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-inverse);
}

.sidebar-brand-subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* Navigation Styles */
.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: var(--space-2);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  font-weight: 500;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-inverse);
  transform: translateX(-2px);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--primary);
  border-radius: 2px;
}

.nav-icon {
  width: 20px;
  text-align: center;
  font-size: 1rem;
}

.nav-text {
  flex: 1;
  font-size: 0.9rem;
}

.nav-badge {
  background: var(--danger);
  color: var(--text-inverse);
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  font-weight: 600;
}

/* Submenu Styles */
.nav-submenu {
  list-style: none;
  padding: 0;
  margin: var(--space-2) 0 0 var(--space-8);
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
}

.nav-submenu.show {
  max-height: 500px;
}

.nav-submenu .nav-link {
  padding: var(--space-2) var(--space-3);
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.nav-submenu .nav-link:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.05);
}

/* Main Content Area - Enhanced Layout */
.main-content {
  flex: 1;
  margin-right: 280px;
  background: var(--bg-secondary);
  min-height: 100vh;
  transition: margin-right var(--transition-normal);
  padding: var(--page-padding);
}

/* Content Container for better max-width and centering */
.content-container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  width: 100%;
}

/* Page Layout Improvements */
.page-wrapper {
  padding: var(--section-gap) 0;
}

.section-spacing {
  margin-bottom: var(--section-gap);
}

.section-spacing:last-child {
  margin-bottom: 0;
}
/* Top Navigation Bar */
.navbar {
  background: var(--bg-navbar);
  border-bottom: 1px solid var(--border-light);
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: background-color var(--transition-normal);
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.navbar-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.navbar-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.navbar-user {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--border-radius);
  background: var(--bg-tertiary);
  transition: background-color var(--transition-fast);
}

.navbar-user:hover {
  background: var(--border-light);
}

.navbar-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-weight: 600;
  font-size: 0.875rem;
}

.navbar-user-info {
  display: flex;
  flex-direction: column;
}

.navbar-user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.navbar-user-role {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Theme Toggle Button */
.theme-toggle {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: var(--space-2);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.theme-toggle:hover {
  background: var(--border-light);
  transform: scale(1.05);
}

.theme-toggle i {
  font-size: 1rem;
  color: var(--text-secondary);
  transition: color var(--transition-fast);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--space-2);
}

/* Content Container */
.content-container {
  padding: var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Header */
.page-header {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--space-8);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
}

.page-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.page-header-info {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.page-title-icon {
  color: var(--primary);
  font-size: 1.75rem;
}

.page-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

.page-header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}
/* ===== CARD COMPONENTS ===== */
.card {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: all var(--transition-normal);
  margin-bottom: var(--card-gap);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  background: var(--bg-tertiary);
  padding: var(--space-5) var(--card-padding);
  border-bottom: 1px solid var(--border-light);
  position: relative;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.card-title-icon {
  color: var(--primary);
  font-size: 1.125rem;
}

.card-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: var(--space-1) 0 0 0;
}

.card-body {
  padding: var(--card-padding);
  line-height: 1.6;
}

.card-footer {
  background: var(--bg-tertiary);
  padding: var(--space-4) var(--card-padding);
  border-top: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
}

/* Card Grid Layouts for Better Organization */
.cards-grid {
  display: grid;
  gap: var(--card-gap);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  margin-bottom: var(--section-gap);
}

.cards-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.cards-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.cards-grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Enhanced Stats Components */
.stats-grid {
  display: grid;
  gap: var(--card-gap);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  margin-bottom: var(--section-gap);
}

.stats-card, .stat-card {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--card-padding);
  text-align: center;
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 120px;
}

.stats-card::before, .stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary);
}

.stats-card:hover, .stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.stats-number, .stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary);
  margin: 0;
  line-height: 1;
  margin-bottom: var(--space-2);
}

.stats-label, .stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Typography Hierarchy */
.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--space-6) 0;
  line-height: 1.2;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
  line-height: 1.3;
}

.subsection-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
  line-height: 1.4;
}

.stats-change {
  font-size: 0.75rem;
  margin-top: var(--space-1);
  font-weight: 600;
}

.stats-change.positive {
  color: var(--success);
}

.stats-change.negative {
  color: var(--danger);
}

/* ===== FORM COMPONENTS ===== */
.form-group {
  margin-bottom: var(--space-5);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-label.required::after {
  content: ' *';
  color: var(--danger);
}

.form-control {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-medium);
  border-radius: var(--border-radius);
  background: var(--bg-card);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-family: var(--font-family-primary);
  transition: all var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  cursor: not-allowed;
}

.form-control.error {
  border-color: var(--danger);
}

.form-control.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-left: 2.5rem;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-help {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  margin-top: var(--space-1);
}

.form-error {
  font-size: 0.75rem;
  color: var(--danger);
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.form-row {
  display: flex;
  gap: var(--space-4);
  align-items: flex-start;
}

.form-col {
  flex: 1;
}
/* ===== BUTTON COMPONENTS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-5);
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 600;
  font-family: var(--font-family-primary);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  line-height: 1.5;
  min-height: 40px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Primary Button */
.btn-primary {
  background: var(--primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Secondary Button */
.btn-secondary {
  background: var(--bg-card);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--border-dark);
}

/* Success Button */
.btn-success {
  background: var(--success);
  color: var(--text-inverse);
}

.btn-success:hover:not(:disabled) {
  background: var(--success-600);
  transform: translateY(-1px);
}

/* Warning Button */
.btn-warning {
  background: var(--warning);
  color: var(--text-inverse);
}

.btn-warning:hover:not(:disabled) {
  background: var(--warning-600);
  transform: translateY(-1px);
}

/* Danger Button */
.btn-danger {
  background: var(--danger);
  color: var(--text-inverse);
}

.btn-danger:hover:not(:disabled) {
  background: var(--danger-600);
  transform: translateY(-1px);
}

/* Info Button */
.btn-info {
  background: var(--info);
  color: var(--text-inverse);
}

.btn-info:hover:not(:disabled) {
  background: var(--info-600);
  transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: 0.75rem;
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: 1rem;
}

.btn-xl {
  padding: var(--space-5) var(--space-8);
  font-size: 1.125rem;
}

/* Outline Buttons */
.btn-outline-primary {
  background: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover:not(:disabled) {
  background: var(--primary);
  color: var(--text-inverse);
}

.btn-outline-secondary {
  background: transparent;
  color: var(--text-secondary);
  border-color: var(--border-medium);
}

.btn-outline-secondary:hover:not(:disabled) {
  background: var(--text-secondary);
  color: var(--text-inverse);
}

/* Ghost Buttons */
.btn-ghost {
  background: transparent;
  border: none;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Icon Buttons */
.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: var(--border-radius);
}

.btn-icon-sm {
  width: 32px;
  height: 32px;
}

.btn-icon-lg {
  width: 48px;
  height: 48px;
}

/* Button Groups */
.btn-group {
  display: inline-flex;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn-group .btn {
  border-radius: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:first-child {
  border-right: 0;
}

.btn-group .btn:last-child {
  border-left: 0;
}
/* ===== TABLE COMPONENTS ===== */
.table-container {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.table th {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  font-weight: 600;
  padding: var(--space-4) var(--space-5);
  text-align: right;
  border-bottom: 1px solid var(--border-light);
  position: sticky;
  top: 0;
  z-index: 10;
}

.table td {
  padding: var(--space-4) var(--space-5);
  border-bottom: 1px solid var(--border-light);
  color: var(--text-primary);
  vertical-align: middle;
}

.table tbody tr {
  transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
  background: var(--bg-tertiary);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* Table Variants */
.table-striped tbody tr:nth-child(even) {
  background: var(--bg-secondary);
}

.table-bordered {
  border: 1px solid var(--border-light);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--border-light);
}

/* Table Actions */
.table-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  justify-content: flex-end;
}

.table-actions .btn {
  padding: var(--space-1) var(--space-2);
  font-size: 0.75rem;
}

/* Responsive Table */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Table Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.active {
  background: var(--success-50);
  color: var(--success-700);
}

.status-badge.inactive {
  background: var(--gray-100);
  color: var(--gray-700);
}

.status-badge.pending {
  background: var(--warning-50);
  color: var(--warning-700);
}

.status-badge.rejected {
  background: var(--danger-50);
  color: var(--danger-700);
}

/* ===== ALERT COMPONENTS ===== */
.alert {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-5);
  border-radius: var(--border-radius);
  border: 1px solid transparent;
  margin-bottom: var(--space-4);
  font-size: 0.875rem;
  line-height: 1.5;
}

.alert-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 0.75rem;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin: 0 0 var(--space-1) 0;
}

.alert-message {
  margin: 0;
  color: inherit;
}

.alert-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  font-size: 1rem;
  opacity: 0.7;
  transition: opacity var(--transition-fast);
}

.alert-close:hover {
  opacity: 1;
}

/* Alert Variants */
.alert-success {
  background: var(--success-50);
  color: var(--success-700);
  border-color: var(--success-200);
}

.alert-success .alert-icon {
  background: var(--success-500);
  color: white;
}

.alert-warning {
  background: var(--warning-50);
  color: var(--warning-700);
  border-color: var(--warning-200);
}

.alert-warning .alert-icon {
  background: var(--warning-500);
  color: white;
}

.alert-danger {
  background: var(--danger-50);
  color: var(--danger-700);
  border-color: var(--danger-200);
}

.alert-danger .alert-icon {
  background: var(--danger-500);
  color: white;
}

.alert-info {
  background: var(--info-50);
  color: var(--info-700);
  border-color: var(--info-200);
}

.alert-info .alert-icon {
  background: var(--info-500);
  color: white;
}
/* ===== MODAL COMPONENTS ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-4);
}

.modal {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-3);
}

/* ===== LOADING COMPONENTS ===== */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
}

.loading-spinner-lg {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  background: var(--bg-card);
  padding: var(--space-8);
  border-radius: var(--border-radius-lg);
  text-align: center;
  box-shadow: var(--shadow-xl);
}

/* ===== UTILITY CLASSES ===== */

/* Spacing */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--space-1) !important; }
.m-2 { margin: var(--space-2) !important; }
.m-3 { margin: var(--space-3) !important; }
.m-4 { margin: var(--space-4) !important; }
.m-5 { margin: var(--space-5) !important; }
.m-6 { margin: var(--space-6) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-1) !important; }
.mt-2 { margin-top: var(--space-2) !important; }
.mt-3 { margin-top: var(--space-3) !important; }
.mt-4 { margin-top: var(--space-4) !important; }
.mt-5 { margin-top: var(--space-5) !important; }
.mt-6 { margin-top: var(--space-6) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-1) !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.mb-5 { margin-bottom: var(--space-5) !important; }
.mb-6 { margin-bottom: var(--space-6) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-1) !important; }
.p-2 { padding: var(--space-2) !important; }
.p-3 { padding: var(--space-3) !important; }
.p-4 { padding: var(--space-4) !important; }
.p-5 { padding: var(--space-5) !important; }
.p-6 { padding: var(--space-6) !important; }

/* Display */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Flexbox */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }

/* Text */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--success) !important; }
.text-warning { color: var(--warning) !important; }
.text-danger { color: var(--danger) !important; }
.text-info { color: var(--info) !important; }
.text-muted { color: var(--text-muted) !important; }

.fw-normal { font-weight: 400 !important; }
.fw-medium { font-weight: 500 !important; }
.fw-semibold { font-weight: 600 !important; }
.fw-bold { font-weight: 700 !important; }

.fs-xs { font-size: 0.75rem !important; }
.fs-sm { font-size: 0.875rem !important; }
.fs-base { font-size: 1rem !important; }
.fs-lg { font-size: 1.125rem !important; }
.fs-xl { font-size: 1.25rem !important; }
.fs-2xl { font-size: 1.5rem !important; }
.fs-3xl { font-size: 1.875rem !important; }
/* Background Colors */
.bg-primary { background: var(--primary) !important; color: var(--text-inverse) !important; }
.bg-secondary { background: var(--text-secondary) !important; color: var(--text-inverse) !important; }
.bg-success { background: var(--success) !important; color: var(--text-inverse) !important; }
.bg-warning { background: var(--warning) !important; color: var(--text-inverse) !important; }
.bg-danger { background: var(--danger) !important; color: var(--text-inverse) !important; }
.bg-info { background: var(--info) !important; color: var(--text-inverse) !important; }

/* Border Radius */
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-full { border-radius: 9999px !important; }

/* Shadows */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

/* Width & Height */
.w-full { width: 100% !important; }
.h-full { height: 100% !important; }
.min-h-screen { min-height: 100vh !important; }

/* ===== SCROLLBAR STYLES ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: 4px;
  transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

::-webkit-scrollbar-corner {
  background: var(--bg-tertiary);
}

/* Firefox Scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--border-medium) var(--bg-tertiary);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
  .content-container {
    max-width: 1320px;
  }
}

/* Desktop (1200px - 1399px) */
@media (max-width: 1399px) {
  .content-container {
    max-width: 1140px;
  }
}

/* Tablet Landscape (992px - 1199px) */
@media (max-width: 1199px) {
  .sidebar {
    width: 240px;
  }

  .main-content {
    margin-right: 240px;
  }

  .content-container {
    max-width: 960px;
    padding: var(--space-4);
  }

  .page-header {
    padding: var(--space-6);
  }

  .form-row {
    flex-direction: column;
    gap: var(--space-3);
  }
}

/* Tablet Portrait (768px - 991px) */
@media (max-width: 991px) {
  .sidebar {
    transform: translateX(100%);
    width: 280px;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content {
    margin-right: 0;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .navbar {
    padding: var(--space-3) var(--space-4);
  }

  .navbar-title {
    font-size: 1.25rem;
  }

  .page-header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .stats-card {
    padding: var(--space-4);
  }

  .stats-number {
    font-size: 2rem;
  }

  .table-responsive {
    font-size: 0.75rem;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-medium);
    margin-bottom: var(--space-1);
  }
}

/* Mobile Landscape (576px - 767px) */
@media (max-width: 767px) {
  .content-container {
    padding: var(--space-3);
  }

  .page-header {
    padding: var(--space-4);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .card-body {
    padding: var(--space-4);
  }

  .card-header,
  .card-footer {
    padding: var(--space-3) var(--space-4);
  }

  .table th,
  .table td {
    padding: var(--space-2) var(--space-3);
  }

  .btn {
    padding: var(--space-2) var(--space-4);
    font-size: 0.8rem;
  }

  .btn-lg {
    padding: var(--space-3) var(--space-5);
    font-size: 0.9rem;
  }

  .modal {
    margin: var(--space-2);
    max-width: calc(100% - var(--space-4));
  }

  .navbar-user-info {
    display: none;
  }
}

/* Mobile Portrait (up to 575px) */
@media (max-width: 575px) {
  .content-container {
    padding: var(--space-2);
  }

  .page-header {
    padding: var(--space-3);
    margin-bottom: var(--space-4);
  }

  .page-title {
    font-size: 1.25rem;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .page-title-icon {
    font-size: 1.5rem;
  }

  .card {
    border-radius: var(--border-radius);
  }

  .card-body {
    padding: var(--space-3);
  }

  .stats-card {
    padding: var(--space-3);
  }

  .stats-number {
    font-size: 1.75rem;
  }

  .table-actions {
    flex-direction: column;
    gap: var(--space-1);
  }

  .table-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .form-control {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .btn-group {
    width: 100%;
  }

  .btn-group .btn {
    flex: 1;
  }

  .navbar-title {
    font-size: 1.125rem;
  }

  .navbar-user {
    padding: var(--space-1) var(--space-2);
  }

  .navbar-user-avatar {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}

/* Print Styles */
@media print {
  .sidebar,
  .navbar,
  .page-header-actions,
  .table-actions,
  .btn,
  .theme-toggle,
  .mobile-menu-toggle {
    display: none !important;
  }

  .main-content {
    margin-right: 0 !important;
  }

  .page-header {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
  }

  .table {
    border-collapse: collapse !important;
  }

  .table th,
  .table td {
    border: 1px solid #000 !important;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== ENHANCED SPACING UTILITIES ===== */
/* Margin utilities */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--space-1) !important; }
.m-2 { margin: var(--space-2) !important; }
.m-3 { margin: var(--space-3) !important; }
.m-4 { margin: var(--space-4) !important; }
.m-5 { margin: var(--space-5) !important; }
.m-6 { margin: var(--space-6) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-1) !important; }
.mt-2 { margin-top: var(--space-2) !important; }
.mt-3 { margin-top: var(--space-3) !important; }
.mt-4 { margin-top: var(--space-4) !important; }
.mt-5 { margin-top: var(--space-5) !important; }
.mt-6 { margin-top: var(--space-6) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-1) !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.mb-5 { margin-bottom: var(--space-5) !important; }
.mb-6 { margin-bottom: var(--space-6) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--space-1) !important; }
.ml-2 { margin-left: var(--space-2) !important; }
.ml-3 { margin-left: var(--space-3) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--space-1) !important; }
.mr-2 { margin-right: var(--space-2) !important; }
.mr-3 { margin-right: var(--space-3) !important; }

/* Padding utilities */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-1) !important; }
.p-2 { padding: var(--space-2) !important; }
.p-3 { padding: var(--space-3) !important; }
.p-4 { padding: var(--space-4) !important; }
.p-5 { padding: var(--space-5) !important; }
.p-6 { padding: var(--space-6) !important; }

/* Gap utilities for flexbox and grid */
.gap-0 { gap: 0 !important; }
.gap-1 { gap: var(--space-1) !important; }
.gap-2 { gap: var(--space-2) !important; }
.gap-3 { gap: var(--space-3) !important; }
.gap-4 { gap: var(--space-4) !important; }
.gap-5 { gap: var(--space-5) !important; }
.gap-6 { gap: var(--space-6) !important; }

/* ===== ENHANCED LAYOUT UTILITIES ===== */
/* Flexbox utilities */
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }
.d-block { display: block !important; }
.d-inline-block { display: inline-block !important; }
.d-none { display: none !important; }

.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }

.flex-1 { flex: 1 !important; }
.flex-auto { flex: auto !important; }
.flex-none { flex: none !important; }

/* Text alignment */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* Position utilities */
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Width utilities */
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

/* ===== ENHANCED COMPONENT STYLES ===== */

/* Quick Links Grid */
.quick-links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--card-gap);
  margin-bottom: var(--section-gap);
}

.quick-link-item {
  display: flex;
}

.quick-link-card {
  display: flex;
  width: 100%;
  padding: var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  color: inherit;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.quick-link-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  text-decoration: none;
  color: inherit;
}

.quick-link-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.quick-link-card:hover::before {
  transform: scaleX(1);
}

.quick-link-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  width: 100%;
}

.quick-link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--primary-light);
  color: var(--primary);
  border-radius: var(--border-radius);
  font-size: 1.25rem;
  flex-shrink: 0;
}

.quick-link-text h6 {
  margin: 0 0 var(--space-1) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.quick-link-text p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Employee Cards Container */
.employee-cards-container {
  margin-bottom: var(--section-gap);
}

.employee-card {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-lg);
  padding: var(--card-padding);
  transition: all var(--transition-normal);
  position: relative;
}

.employee-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Filter Sections */
.filter-section {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--card-gap);
}

.filter-header {
  padding: var(--space-4) var(--card-padding);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filter-body {
  padding: var(--card-padding);
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--element-gap);
}

.filter-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
  justify-content: flex-end;
  padding: var(--space-4) var(--card-padding);
  border-top: 1px solid var(--border-light);
  background: var(--bg-tertiary);
}

/* Search Section */
.search-section {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-lg);
  padding: var(--card-padding);
  margin-bottom: var(--card-gap);
}

.search-input-group {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.search-input-group .form-control {
  flex: 1;
}

/* Enhanced Table Styles */
.table-container {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  margin-bottom: var(--section-gap);
}

.table-header {
  padding: var(--space-4) var(--card-padding);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.employee-info-cell {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.employee-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.employee-avatar .avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.employee-details {
  flex: 1;
}

.employee-name {
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
}

.employee-name:hover {
  color: var(--primary);
  text-decoration: none;
}

/* ===== ENHANCED TOAST AND NOTIFICATION STYLES ===== */
.toast-container {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: 1050;
}

.toast {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--space-2);
  min-width: 300px;
}

.toast-success {
  border-left: 4px solid var(--success);
}

.toast-error {
  border-left: 4px solid var(--danger);
}

.toast-warning {
  border-left: 4px solid var(--warning);
}

.toast-info {
  border-left: 4px solid var(--info);
}

.toast-content {
  display: flex;
  align-items: center;
  padding: var(--space-4);
}

.toast-body {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex: 1;
  color: var(--text-primary);
}

.toast-body i {
  color: var(--success);
  font-size: 1.125rem;
}

.btn-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  margin-left: var(--space-2);
}

.btn-close:hover {
  color: var(--text-primary);
}

/* ===== ENHANCED LOADING INDICATOR ===== */
.loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1040;
  height: 3px;
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: var(--bg-tertiary);
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--primary-hover));
  animation: loading-progress 2s ease-in-out infinite;
}

@keyframes loading-progress {
  0% {
    width: 0%;
    margin-left: 0%;
  }
  50% {
    width: 75%;
    margin-left: 25%;
  }
  100% {
    width: 0%;
    margin-left: 100%;
  }
}

/* ===== ENHANCED FAB AND QUICK ACTIONS ===== */
.quick-actions-fab {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  z-index: 1030;
}

.fab-button {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  border: none;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  transition: all var(--transition-normal);
}

.fab-button:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.quick-actions-menu {
  position: fixed;
  bottom: 90px;
  right: var(--space-6);
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-4);
  min-width: 250px;
  z-index: 1020;
}

.menu-title {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 var(--space-3) 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  background: none;
  border: none;
  width: 100%;
  text-align: right;
  cursor: pointer;
}

.menu-item:hover {
  background: var(--bg-tertiary);
  color: var(--primary);
  text-decoration: none;
}

.menu-item i {
  color: var(--primary);
  width: 16px;
  text-align: center;
}

/* ===== ENHANCED MODAL STYLES ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.modal-dialog {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  padding: var(--space-5) var(--space-6);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.modal-title i {
  color: var(--primary);
}

.modal-body {
  padding: var(--space-6);
  max-height: 60vh;
  overflow-y: auto;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3);
  background: var(--bg-tertiary);
  border-radius: var(--border-radius);
}

.shortcut-keys {
  display: flex;
  gap: var(--space-1);
  align-items: center;
}

.shortcut-keys kbd {
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid var(--border-medium);
}

.shortcut-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* ===== ENHANCED RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  /* Page wrapper adjustments */
  .page-wrapper {
    padding: var(--space-4);
  }

  .section-spacing {
    margin-bottom: var(--space-8);
  }

  /* Page header mobile adjustments */
  .page-header-content {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .page-header-actions {
    width: 100%;
  }

  .btn-toolbar {
    flex-direction: column;
    gap: var(--space-2);
  }

  .btn-toolbar .btn {
    width: 100%;
    justify-content: center;
  }

  /* Stats grid mobile adjustments */
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-3);
  }

  .stats-card {
    padding: var(--space-4);
  }

  /* Cards grid mobile adjustments */
  .cards-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .quick-links-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  /* Filter grid mobile adjustments */
  .filter-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .filter-actions {
    flex-direction: column;
    gap: var(--space-2);
  }

  .filter-actions .btn {
    width: 100%;
  }

  /* Table responsive adjustments */
  .table-responsive {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
  }

  /* Toast mobile adjustments */
  .toast-container {
    top: var(--space-2);
    right: var(--space-2);
    left: var(--space-2);
  }

  .toast {
    min-width: auto;
    width: 100%;
  }

  /* FAB mobile adjustments */
  .quick-actions-fab {
    bottom: var(--space-4);
    right: var(--space-4);
  }

  .quick-actions-menu {
    bottom: 70px;
    right: var(--space-4);
    left: var(--space-4);
    min-width: auto;
  }

  /* Modal mobile adjustments */
  .modal-dialog {
    width: 95%;
    margin: var(--space-4);
  }

  .shortcuts-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  /* Extra small mobile adjustments */
  .page-wrapper {
    padding: var(--space-3);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .card-padding {
    padding: var(--space-4);
  }

  .quick-link-content {
    gap: var(--space-3);
  }

  .quick-link-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* ===== ENHANCED PRINT STYLES ===== */
@media print {
  .sidebar,
  .navbar,
  .page-header-actions,
  .card-header-actions,
  .filter-actions,
  .quick-actions-fab,
  .toast-container,
  .loading-indicator,
  .btn,
  .dropdown {
    display: none !important;
  }

  .main-content {
    margin-right: 0 !important;
    padding: 0 !important;
  }

  .page-wrapper {
    padding: 0 !important;
  }

  .card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
    margin-bottom: 1rem !important;
    break-inside: avoid;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 1rem !important;
  }

  .table {
    font-size: 12px !important;
  }

  .page-title {
    color: #000 !important;
    font-size: 18px !important;
    margin-bottom: 1rem !important;
  }
}
