<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <title>{% block title %}نظام الموارد البشرية - ElDawliya{% endblock %}</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Modern HR Design System -->
    <link rel="stylesheet" href="{% static 'Hr/css/hr_clean.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <!-- Brand Section -->
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="sidebar-brand-text">
                    <h1 class="sidebar-brand-title">الدولية</h1>
                    <p class="sidebar-brand-subtitle">الموارد البشرية</p>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="nav-menu">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="{% url 'Hr:dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <span class="nav-text">لوحة التحكم</span>
                        </a>
                    </li>

                    <!-- Company Structure -->
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-submenu="company">
                            <i class="nav-icon fas fa-sitemap"></i>
                            <span class="nav-text">هيكل الشركة</span>
                            <i class="fas fa-chevron-down" style="margin-right: auto; font-size: 0.75rem;"></i>
                        </a>
                        <ul class="nav-submenu" id="company-submenu">
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-building"></i>
                                    <span class="nav-text">الشركات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-code-branch"></i>
                                    <span class="nav-text">الفروع</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-users-cog"></i>
                                    <span class="nav-text">الأقسام</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-user-tie"></i>
                                    <span class="nav-text">المناصب</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Employee Management -->
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-submenu="employees">
                            <i class="nav-icon fas fa-users"></i>
                            <span class="nav-text">إدارة الموظفين</span>
                            <i class="fas fa-chevron-down" style="margin-right: auto; font-size: 0.75rem;"></i>
                        </a>
                        <ul class="nav-submenu" id="employees-submenu">
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-list"></i>
                                    <span class="nav-text">قائمة الموظفين</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-user-plus"></i>
                                    <span class="nav-text">إضافة موظف</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Attendance -->
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-submenu="attendance">
                            <i class="nav-icon fas fa-clock"></i>
                            <span class="nav-text">الحضور والانصراف</span>
                            <i class="fas fa-chevron-down" style="margin-right: auto; font-size: 0.75rem;"></i>
                        </a>
                        <ul class="nav-submenu" id="attendance-submenu">
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-list-alt"></i>
                                    <span class="nav-text">سجلات الحضور</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-business-time"></i>
                                    <span class="nav-text">الورديات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-chart-line"></i>
                                    <span class="nav-text">تقارير الحضور</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Leave Management -->
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-submenu="leave">
                            <i class="nav-icon fas fa-calendar-times"></i>
                            <span class="nav-text">إدارة الإجازات</span>
                            <i class="fas fa-chevron-down" style="margin-right: auto; font-size: 0.75rem;"></i>
                        </a>
                        <ul class="nav-submenu" id="leave-submenu">
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-calendar-check"></i>
                                    <span class="nav-text">طلبات الإجازات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-tags"></i>
                                    <span class="nav-text">أنواع الإجازات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-balance-scale"></i>
                                    <span class="nav-text">أرصدة الإجازات</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Payroll -->
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-submenu="payroll">
                            <i class="nav-icon fas fa-money-bill-wave"></i>
                            <span class="nav-text">نظام الرواتب</span>
                            <i class="fas fa-chevron-down" style="margin-right: auto; font-size: 0.75rem;"></i>
                        </a>
                        <ul class="nav-submenu" id="payroll-submenu">
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-receipt"></i>
                                    <span class="nav-text">سجلات الرواتب</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-calendar-alt"></i>
                                    <span class="nav-text">فترات الرواتب</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">
                                    <i class="nav-icon fas fa-puzzle-piece"></i>
                                    <span class="nav-text">مكونات الراتب</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation Bar -->
            <nav class="navbar">
                <div class="navbar-left">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="navbar-title">{% block page_title %}نظام الموارد البشرية{% endblock %}</h1>
                        <p class="navbar-subtitle">{% block page_subtitle %}إدارة شاملة للموارد البشرية{% endblock %}</p>
                    </div>
                </div>

                <div class="navbar-right">
                    <!-- Theme Toggle -->
                    <button id="themeToggle" class="theme-toggle" title="تبديل الوضع الليلي/النهاري">
                        <i id="themeIcon" class="fas fa-moon"></i>
                    </button>

                    <!-- User Info -->
                    <div class="navbar-user">
                        <div class="navbar-user-avatar">
                            {{ user.get_full_name.0|default:user.username.0|upper }}
                        </div>
                        <div class="navbar-user-info">
                            <p class="navbar-user-name">{{ user.get_full_name|default:user.username }}</p>
                            <p class="navbar-user-role">مدير النظام</p>
                        </div>
                    </div>

                    <!-- Logout Button -->
                    <a href="{% url 'admin:logout' %}" class="btn btn-danger btn-sm">
                        <i class="fas fa-sign-out-alt"></i>
                        <span class="d-none d-md-inline">تسجيل الخروج</span>
                    </a>
                </div>
            </nav>

            <!-- Content Container -->
            <div class="content-container">
                <!-- Messages -->
                {% if messages %}
                    <div class="messages-container mb-4">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">
                                <div class="alert-icon">
                                    <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-circle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                                </div>
                                <div class="alert-content">
                                    <p class="alert-message">{{ message }}</p>
                                </div>
                                <button type="button" class="alert-close" onclick="this.parentElement.remove()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Page Header -->
                {% block page_header %}{% endblock %}

                <!-- Main Content Area -->
                <div class="fade-in">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- Theme Manager Script -->
    <script src="{% static 'Hr/js/theme-manager.js' %}"></script>

    <!-- Navigation & Mobile Menu Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.getElementById('sidebar');

            if (mobileMenuToggle && sidebar) {
                mobileMenuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });

                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function(e) {
                    if (window.innerWidth <= 991) {
                        if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                            sidebar.classList.remove('show');
                        }
                    }
                });

                // Close sidebar on window resize to desktop
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 991) {
                        sidebar.classList.remove('show');
                    }
                });
            }

            // Submenu toggles
            const submenuToggles = document.querySelectorAll('[data-submenu]');
            submenuToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const submenuId = this.getAttribute('data-submenu') + '-submenu';
                    const submenu = document.getElementById(submenuId);
                    const chevron = this.querySelector('.fa-chevron-down');

                    if (submenu) {
                        submenu.classList.toggle('show');
                        chevron.style.transform = submenu.classList.contains('show') ? 'rotate(180deg)' : 'rotate(0deg)';
                    }
                });
            });

            // Auto-hide alerts
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-20px)';
                    setTimeout(() => alert.remove(), 300);
                });
            }, 5000);

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // CSRF Token helper
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            // Make CSRF token globally available
            window.csrftoken = getCookie('csrftoken');
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
