{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}
{% load form_utils %}

{% block title %}أداء قسم {{ department.dept_name }} - نظام الدولية{% endblock %}

{% block page_title %}أداء قسم: {{ department.dept_name }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:departments:department_list' %}">الأقسام</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:departments:department_detail' department.dept_code %}">{{ department.dept_name }}</a></li>
<li class="breadcrumb-item active">أداء القسم</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-chart-line text-primary me-2"></i>أداء قسم {{ department.dept_name }}
                </h5>
                <a href="{% url 'Hr:departments:department_detail' department.dept_code %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للقسم
                </a>
            </div>
            <div class="card-body">
                <!-- Performance Overview -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card border h-100">
                            <div class="card-body text-center">
                                <div class="display-4 text-success mb-2">{{ attendance_rate }}%</div>
                                <h6 class="text-muted">معدل الحضور</h6>
                                <div class="progress mt-3" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ attendance_rate }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border h-100">
                            <div class="card-body text-center">
                                <div class="display-4 text-primary mb-2">{{ task_completion_rate }}%</div>
                                <h6 class="text-muted">إنجاز المهام</h6>
                                <div class="progress mt-3" style="height: 8px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: {{ task_completion_rate }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border h-100">
                            <div class="card-body text-center">
                                <div class="display-4 text-warning mb-2">{{ evaluation_average }}/5</div>
                                <h6 class="text-muted">متوسط التقييم</h6>
                                <div class="d-flex justify-content-center mt-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= evaluation_average|floatformat:"0" %}
                                        <i class="fas fa-star text-warning mx-1"></i>
                                        {% elif forloop.counter <= evaluation_average|add:"0.5"|floatformat:"0" %}
                                        <i class="fas fa-star-half-alt text-warning mx-1"></i>
                                        {% else %}
                                        <i class="far fa-star text-warning mx-1"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Charts -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">أداء القسم الشهري</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height: 300px;">
                                    <canvas id="monthlyPerformanceChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">مقارنة مع الأقسام الأخرى</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height: 300px;">
                                    <canvas id="departmentComparisonChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Performers -->
                <div class="card border mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">أفضل الموظفين أداءً</h6>
                    </div>
                    <div class="card-body p-0">
                        {% if employees %}
                        <div class="table-responsive">
                            <table class="table table-hover align-middle mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="py-3 px-4">الموظف</th>
                                        <th class="py-3 px-4">المسمى الوظيفي</th>
                                        <th class="py-3 px-4">معدل الحضور</th>
                                        <th class="py-3 px-4">إنجاز المهام</th>
                                        <th class="py-3 px-4">التقييم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for employee in employees|slice:":5" %}
                                    <tr>
                                        <td class="px-4">
                                            <div class="d-flex align-items-center">
                                                {% if employee.emp_image %}
                                                <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}" class="rounded-circle me-2 object-fit-cover" width="40" height="40">
                                                {% else %}
                                                <div class="avatar-sm bg-secondary text-white me-2">{{ employee.emp_first_name|slice:":1"|upper }}</div>
                                                {% endif %}
                                                <div>
                                                    <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="fw-medium d-block text-decoration-none text-dark">{{ employee.emp_full_name|default:employee.emp_first_name }}</a>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-4">{{ employee.jop_name|default:"-" }}</td>
                                        <td class="px-4">
                                            <div class="progress" style="height: 6px; width: 100px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: {% widthratio 80 100 100 %}%"></div>
                                            </div>
                                            <small class="text-muted">80%</small>
                                        </td>
                                        <td class="px-4">
                                            <div class="progress" style="height: 6px; width: 100px;">
                                                <div class="progress-bar bg-primary" role="progressbar" style="width: {% widthratio 90 100 100 %}%"></div>
                                            </div>
                                            <small class="text-muted">90%</small>
                                        </td>
                                        <td class="px-4">
                                            <div class="d-flex">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star-half-alt text-warning"></i>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x mb-3 text-muted"></i>
                            <p class="mb-0">لا يوجد موظفين مسجلين في هذا القسم حالياً.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Improvement Areas -->
                <div class="card border">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">مجالات التحسين</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="rounded-circle bg-danger-subtle p-3">
                                            <i class="fas fa-clock text-danger"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">تحسين الالتزام بمواعيد العمل</h6>
                                        <p class="text-muted small mb-0">تقليل التأخير في الحضور والانصراف</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="rounded-circle bg-warning-subtle p-3">
                                            <i class="fas fa-tasks text-warning"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">زيادة إنتاجية الفريق</h6>
                                        <p class="text-muted small mb-0">تحسين معدل إنجاز المهام اليومية</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="rounded-circle bg-info-subtle p-3">
                                            <i class="fas fa-graduation-cap text-info"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">تطوير المهارات الفنية</h6>
                                        <p class="text-muted small mb-0">زيادة التدريب على التقنيات الحديثة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Monthly Performance Chart
        const monthlyCtx = document.getElementById('monthlyPerformanceChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'معدل الحضور',
                    data: [92, 94, 89, 96, 93, 95],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'إنجاز المهام',
                    data: [78, 82, 80, 85, 83, 85],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'التقييم',
                    data: [3.8, 4.0, 4.1, 4.2, 4.3, 4.2],
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // Department Comparison Chart
        const comparisonCtx = document.getElementById('departmentComparisonChart').getContext('2d');
        const comparisonChart = new Chart(comparisonCtx, {
            type: 'radar',
            data: {
                labels: [
                    'الحضور',
                    'إنجاز المهام',
                    'التقييم',
                    'التعاون',
                    'الإبداع',
                    'الالتزام'
                ],
                datasets: [{
                    label: '{{ department.dept_name }}',
                    data: [95, 85, 84, 90, 75, 88],
                    fill: true,
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgb(255, 99, 132)',
                    pointBackgroundColor: 'rgb(255, 99, 132)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgb(255, 99, 132)'
                }, {
                    label: 'متوسط الأقسام',
                    data: [90, 80, 80, 85, 70, 85],
                    fill: true,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgb(54, 162, 235)',
                    pointBackgroundColor: 'rgb(54, 162, 235)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgb(54, 162, 235)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                elements: {
                    line: {
                        borderWidth: 3
                    }
                },
                scales: {
                    r: {
                        angleLines: {
                            display: true
                        },
                        suggestedMin: 50,
                        suggestedMax: 100
                    }
                }
            }
        });
    });
</script>
{% endblock %}