{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}حذف الوظيفة - {{ job.jop_name }} - نظام الدولية{% endblock %}

{% block page_title %}حذف الوظيفة: {{ job.jop_name }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:jobs:job_list' %}">الوظائف</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:jobs:job_detail' job.jop_code %}">{{ job.jop_name }}</a></li>
<li class="breadcrumb-item active">حذف</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-danger text-white py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد حذف الوظيفة
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-warning mb-4">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i> تحذير!</h5>
                    <p>أنت على وشك حذف الوظيفة التالية:</p>
                    <ul>
                        <li><strong>رمز الوظيفة:</strong> {{ job.jop_code }}</li>
                        <li><strong>اسم الوظيفة:</strong> {{ job.jop_name }}</li>
                        <li><strong>القسم:</strong> {{ job.department.dept_name|default:"غير محدد" }}</li>
                    </ul>
                    <p class="mb-0">هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>

                {% if employees.exists %}
                <div class="alert alert-danger mb-4">
                    <h5><i class="fas fa-times-circle me-2"></i> لا يمكن الحذف!</h5>
                    <p class="mb-0">لا يمكن حذف هذه الوظيفة لأنها مرتبطة بـ {{ employees.count }} موظف. يجب نقل الموظفين إلى وظائف أخرى أولاً.</p>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <a href="{% url 'Hr:jobs:job_detail' job.jop_code %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i> العودة للتفاصيل
                    </a>
                    <a href="{% url 'Hr:jobs:job_edit' job.jop_code %}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i> تعديل الوظيفة
                    </a>
                </div>
                {% else %}
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{% url 'Hr:jobs:job_detail' job.jop_code %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-1"></i> تأكيد الحذف
                        </button>
                    </div>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
