WARNING 2025-05-25 02:38:20,261 services 12208 10840 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 02:38:30,633 services 12208 6176 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 02:40:22,228 services 12208 1924 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 02:41:23,572 services 12208 6260 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 02:43:40,087 services 12208 8328 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 02:43:44,288 services 12208 4280 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 02:51:48,835 services 10652 13092 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 03:02:01,644 services 7620 6932 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 03:08:37,445 services 13308 10668 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 03:09:06,383 services 13308 11880 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 03:24:22,382 services 13184 12152 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 03:25:09,909 services 13184 10100 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 03:27:09,283 services 13184 10416 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 03:28:04,415 services 13184 11652 Gemini AI is not properly configured. Check API key and dependencies.
WARNING 2025-05-25 03:28:17,129 services 13184 10096 Gemini AI is not properly configured. Check API key and dependencies.
INFO 2025-05-25 03:44:34,112 services 12628 6532 Using Gemini configuration for user testadmin, API key: your_..., model: gemini-1.5-flash
INFO 2025-05-25 03:44:34,113 services 12628 6532 Gemini AI configured successfully with model: gemini-1.5-flash
ERROR 2025-05-25 03:44:34,585 services 12628 6532 Error generating Gemini response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "E:\Joint Program\24-05-2025\ElDawliya_Sys\api\services.py", line 120, in generate_response
    response = self.model.generate_content(
        prompt,
        generation_config=generation_config,
        safety_settings=safety_settings
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 830, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]

INFO 2025-05-25 03:45:35,646 services 6316 10976 Using Gemini configuration for user testadmin, API key: your_..., model: gemini-1.5-flash
INFO 2025-05-25 03:45:35,646 services 6316 10976 Gemini AI configured successfully with model: gemini-1.5-flash
ERROR 2025-05-25 03:45:35,985 services 6316 10976 Error generating Gemini response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "E:\Joint Program\24-05-2025\ElDawliya_Sys\api\services.py", line 120, in generate_response
    response = self.model.generate_content(
        prompt,
        generation_config=generation_config,
        safety_settings=safety_settings
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 830, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]

INFO 2025-05-25 03:47:45,640 services 6336 10884 Found Gemini provider: 1
INFO 2025-05-25 03:47:45,647 services 6336 10884 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-25 03:47:45,648 services 6336 10884 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-25 03:52:21,150 services 6336 11564 Found Gemini provider: 1
INFO 2025-05-25 03:52:21,153 services 6336 11564 Using Gemini configuration for user Gamal, API key: your_..., model: gemini-1.5-flash
INFO 2025-05-25 03:52:21,153 services 6336 11564 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-25 03:56:23,698 services 10268 328 Found Gemini provider: 1
INFO 2025-05-25 03:56:23,701 services 10268 328 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-25 03:56:23,701 services 10268 328 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-25 03:57:19,135 services 10268 12304 Found Gemini provider: 1
INFO 2025-05-25 03:57:19,139 services 10268 12304 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-25 03:57:19,140 services 10268 12304 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-25 03:57:23,521 services 10268 6868 Found Gemini provider: 1
INFO 2025-05-25 03:57:23,523 services 10268 6868 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-25 03:57:23,524 services 10268 6868 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-25 16:47:12,239 services 9552 10404 Found Gemini provider: 1
INFO 2025-05-25 16:47:12,242 services 9552 10404 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-25 16:47:12,243 services 9552 10404 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-25 16:47:20,138 services 9552 12784 Found Gemini provider: 1
INFO 2025-05-25 16:47:20,141 services 9552 12784 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-25 16:47:20,141 services 9552 12784 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-25 16:47:21,705 services 9552 11540 Found Gemini provider: 1
INFO 2025-05-25 16:47:21,707 services 9552 11540 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-25 16:47:21,708 services 9552 11540 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-25 17:24:28,313 services 9552 12760 Found Gemini provider: 1
WARNING 2025-05-25 17:24:28,315 services 9552 12760 No active Gemini configuration found for user Ramadan
WARNING 2025-05-25 17:24:28,315 services 9552 12760 Gemini AI is not properly configured. Missing: API key
INFO 2025-05-25 17:27:14,639 services 9552 4720 Found Gemini provider: 1
INFO 2025-05-25 17:27:14,642 services 9552 4720 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-25 17:27:14,642 services 9552 4720 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:01:28,993 services 8728 1540 Found Gemini provider: 1
INFO 2025-05-27 20:01:29,001 services 8728 1540 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:01:29,001 services 8728 1540 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:08:25,890 services 3400 6104 Found Gemini provider: 1
INFO 2025-05-27 20:08:25,892 services 3400 6104 Using Gemini configuration for user testadmin, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:08:25,893 services 3400 6104 Gemini AI configured successfully with model: gemini-1.5-flash
ERROR 2025-05-27 20:08:26,734 services 3400 6104 Error generating Gemini response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "E:\Joint Program\24-05-2025\ElDawliya_Sys\api\services.py", line 142, in generate_response
    response = self.model.generate_content(
        prompt,
        generation_config=generation_config,
        safety_settings=safety_settings
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 830, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]

INFO 2025-05-27 20:18:02,909 services 6128 11108 Found Gemini provider: 1
INFO 2025-05-27 20:18:02,912 services 6128 11108 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:18:02,913 services 6128 11108 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:18:13,472 services 6128 9196 Found Gemini provider: 1
INFO 2025-05-27 20:18:13,475 services 6128 9196 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:18:13,475 services 6128 9196 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:18:41,878 services 6128 10488 Found Gemini provider: 1
INFO 2025-05-27 20:18:41,881 services 6128 10488 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:18:41,881 services 6128 10488 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:19:31,389 services 6128 5352 Found Gemini provider: 1
INFO 2025-05-27 20:19:31,391 services 6128 5352 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:19:31,392 services 6128 5352 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:20:14,003 services 6128 10436 Found Gemini provider: 1
INFO 2025-05-27 20:20:14,006 services 6128 10436 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:20:14,006 services 6128 10436 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:20:51,577 services 6128 9604 Found Gemini provider: 1
INFO 2025-05-27 20:20:51,579 services 6128 9604 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:20:51,580 services 6128 9604 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:27:52,416 services 7872 4128 Found Gemini provider: 1
INFO 2025-05-27 20:27:52,419 services 7872 4128 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:27:52,420 services 7872 4128 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:28:19,785 services 7872 2544 Found Gemini provider: 1
INFO 2025-05-27 20:28:19,787 services 7872 2544 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:28:19,787 services 7872 2544 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:28:31,463 services 7872 4992 Found Gemini provider: 1
INFO 2025-05-27 20:28:31,465 services 7872 4992 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:28:31,466 services 7872 4992 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:43:17,934 services 5216 10664 Found Gemini provider: 1
INFO 2025-05-27 20:43:17,937 services 5216 10664 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:43:17,937 services 5216 10664 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:43:38,764 services 5216 3636 Found Gemini provider: 1
INFO 2025-05-27 20:43:38,767 services 5216 3636 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:43:38,767 services 5216 3636 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:43:54,935 services 5216 3552 Found Gemini provider: 1
INFO 2025-05-27 20:43:54,938 services 5216 3552 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:43:54,938 services 5216 3552 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:44:01,121 services 5216 8408 Found Gemini provider: 1
INFO 2025-05-27 20:44:01,123 services 5216 8408 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:44:01,124 services 5216 8408 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:44:41,588 services 5216 9444 Found Gemini provider: 1
INFO 2025-05-27 20:44:41,590 services 5216 9444 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:44:41,590 services 5216 9444 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:53:33,358 services 5232 5668 Found Gemini provider: 1
INFO 2025-05-27 20:53:33,360 services 5232 5668 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:53:33,361 services 5232 5668 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:53:38,453 services 5232 9108 Found Gemini provider: 1
INFO 2025-05-27 20:53:38,455 services 5232 9108 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:53:38,455 services 5232 9108 Gemini AI configured successfully with model: gemini-1.5-flash
ERROR 2025-05-27 20:53:38,783 services 5232 9108 Error generating Gemini response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "E:\Joint Program\24-05-2025\ElDawliya_Sys\api\services.py", line 142, in generate_response
    response = self.model.generate_content(
        prompt,
        generation_config=generation_config,
        safety_settings=safety_settings
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 830, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]

INFO 2025-05-27 20:54:53,119 services 5232 8556 Found Gemini provider: 1
INFO 2025-05-27 20:54:53,121 services 5232 8556 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:54:53,121 services 5232 8556 Gemini AI configured successfully with model: gemini-1.5-flash
ERROR 2025-05-27 20:54:53,358 services 5232 8556 Error generating Gemini response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "E:\Joint Program\24-05-2025\ElDawliya_Sys\api\services.py", line 142, in generate_response
    response = self.model.generate_content(
        prompt,
        generation_config=generation_config,
        safety_settings=safety_settings
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 830, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]

INFO 2025-05-27 20:59:06,303 services 5232 6348 Found Gemini provider: 1
INFO 2025-05-27 20:59:06,305 services 5232 6348 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:59:06,306 services 5232 6348 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:59:13,110 services 5232 11020 Found Gemini provider: 1
INFO 2025-05-27 20:59:13,113 services 5232 11020 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:59:13,113 services 5232 11020 Gemini AI configured successfully with model: gemini-1.5-flash
ERROR 2025-05-27 20:59:13,362 services 5232 11020 Error generating Gemini response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "E:\Joint Program\24-05-2025\ElDawliya_Sys\api\services.py", line 142, in generate_response
    response = self.model.generate_content(
        prompt,
        generation_config=generation_config,
        safety_settings=safety_settings
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 830, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]

INFO 2025-05-27 20:59:21,563 services 5232 3300 Found Gemini provider: 1
INFO 2025-05-27 20:59:21,566 services 5232 3300 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:59:21,566 services 5232 3300 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 20:59:46,997 services 5232 2060 Found Gemini provider: 1
INFO 2025-05-27 20:59:46,999 services 5232 2060 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 20:59:46,999 services 5232 2060 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 21:45:48,749 services 6256 11792 Found Gemini provider: 1
INFO 2025-05-27 21:45:48,752 services 6256 11792 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 21:45:48,753 services 6256 11792 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 21:46:29,918 services 6256 11488 Found Gemini provider: 1
INFO 2025-05-27 21:46:29,921 services 6256 11488 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 21:46:29,921 services 6256 11488 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 21:47:31,590 services 6256 6748 Found Gemini provider: 1
INFO 2025-05-27 21:47:31,593 services 6256 6748 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 21:47:31,593 services 6256 6748 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 21:48:17,622 services 6256 3024 Found Gemini provider: 1
INFO 2025-05-27 21:48:17,624 services 6256 3024 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 21:48:17,625 services 6256 3024 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 21:49:20,275 services 6256 9444 Found Gemini provider: 1
INFO 2025-05-27 21:49:20,277 services 6256 9444 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 21:49:20,278 services 6256 9444 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 21:49:30,193 services 6256 9012 Found Gemini provider: 1
INFO 2025-05-27 21:49:30,196 services 6256 9012 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 21:49:30,196 services 6256 9012 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-27 21:52:25,525 services 10604 11712 Found Gemini provider: 1
INFO 2025-05-27 21:52:25,529 services 10604 11712 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-27 21:52:25,529 services 10604 11712 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-28 14:40:39,996 services 2528 3560 Found Gemini provider: 1
INFO 2025-05-28 14:40:40,011 services 2528 3560 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-28 14:40:40,011 services 2528 3560 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-28 14:41:11,260 services 2528 4868 Found Gemini provider: 1
INFO 2025-05-28 14:41:11,262 services 2528 4868 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-28 14:41:11,263 services 2528 4868 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-28 14:42:48,788 services 2528 6976 Found Gemini provider: 1
INFO 2025-05-28 14:42:48,790 services 2528 6976 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-28 14:42:48,791 services 2528 6976 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-28 14:45:09,053 services 2528 1352 Found Gemini provider: 1
INFO 2025-05-28 14:45:09,055 services 2528 1352 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-28 14:45:09,056 services 2528 1352 Gemini AI configured successfully with model: gemini-1.5-flash
INFO 2025-05-28 15:58:57,009 services 9248 10680 Found Gemini provider: 1
INFO 2025-05-28 15:58:57,012 services 9248 10680 Using Gemini configuration for user Gamal, API key: AIzaS..., model: gemini-1.5-flash
INFO 2025-05-28 15:58:57,013 services 9248 10680 Gemini AI configured successfully with model: gemini-1.5-flash
ERROR 2025-06-29 13:25:23,487 views 7008 4524 Error getting dashboard data: Cannot resolve keyword 'department' into field. Choices are: approval_date, approved_by, approved_by_id, id, items, notes, request_date, request_number, requested_by, requested_by_id, status, vendor, vendor_id
