{% extends 'tasks/base_tasks.html' %}
{% load static %}

{% block title %}لوحة تحكم المهام - نظام الدولية{% endblock %}

{% block page_title %}لوحة تحكم المهام{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">لوحة تحكم المهام</li>
{% endblock %}

{% block content %}
<div class="section-spacing">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-header-content">
            <div class="page-title">
                <i class="fas fa-tasks"></i>
                <span>لوحة تحكم المهام</span>
            </div>
            <div class="page-subtitle">نظرة عامة على المهام والأنشطة</div>
        </div>
        <div class="page-actions">
            <a href="{% url 'tasks:task_create' %}" class="btn btn-primary btn-with-icon">
                <i class="fas fa-plus"></i>
                <span>مهمة جديدة</span>
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-icon">
                <i class="fas fa-list-check"></i>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">إجمالي المهام</div>
                <div class="stats-card-value">{{ total_tasks }}</div>
            </div>
        </div>

        <div class="stats-card stats-card-success">
            <div class="stats-card-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">المهام المكتملة</div>
                <div class="stats-card-value">{{ completed_tasks }}</div>
            </div>
        </div>

        <div class="stats-card stats-card-warning">
            <div class="stats-card-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">المهام المعلقة</div>
                <div class="stats-card-value">{{ pending_tasks }}</div>
            </div>
        </div>

        <div class="stats-card stats-card-danger">
            <div class="stats-card-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">المهام المتأخرة</div>
                <div class="stats-card-value">{{ overdue_tasks }}</div>
            </div>
        </div>
    </div>
    }

    .task-item {
        transition: all 0.2s ease;
        border-left: 4px solid transparent;
    }

    .task-item:hover {
        background-color: #f8f9fa;
        border-left-color: #007bff;
        transform: translateX(5px);
    }

    .progress-ring {
        width: 120px;
        height: 120px;
    }

    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Enhanced Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card stats-card h-100" style="--icon-color-1: #667eea; --icon-color-2: #764ba2;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted mb-2">إجمالي المهام</h6>
                        <h2 class="mb-0 text-primary">{{ total_tasks }}</h2>
                        <small class="text-muted">
                            {% if user_is_superuser %}جميع المهام في النظام{% else %}مهامك الشخصية{% endif %}
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-tasks fa-2x text-white"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tasks:list' %}" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-eye me-1"></i> عرض المهام
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card stats-card h-100" style="--icon-color-1: #11998e; --icon-color-2: #38ef7d;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted mb-2">المهام المكتملة</h6>
                        <h2 class="mb-0 text-success">{{ completed_tasks }}</h2>
                        <small class="text-success">
                            {% if total_tasks > 0 %}
                                {{ completion_rate }}% معدل الإنجاز
                            {% else %}
                                لا توجد مهام
                            {% endif %}
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-check-circle fa-2x text-white"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tasks:completed' %}" class="btn btn-sm btn-outline-success w-100">
                        <i class="fas fa-eye me-1"></i> عرض المكتملة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card stats-card h-100" style="--icon-color-1: #3742fa; --icon-color-2: #2f3542;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted mb-2">قيد التنفيذ</h6>
                        <h2 class="mb-0 text-info">{{ in_progress_tasks }}</h2>
                        <small class="text-info">
                            {% if total_tasks > 0 %}
                                {{ in_progress_tasks|floatformat:0 }}/{{ total_tasks }} مهمة
                            {% else %}
                                لا توجد مهام
                            {% endif %}
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-spinner fa-2x text-white"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tasks:list' %}?status=in_progress" class="btn btn-sm btn-outline-info w-100">
                        <i class="fas fa-eye me-1"></i> عرض الجارية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card stats-card h-100" style="--icon-color-1: #ff3838; --icon-color-2: #ff9500;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted mb-2">المهام المتأخرة</h6>
                        <h2 class="mb-0 text-danger">{{ overdue_tasks }}</h2>
                        <small class="text-danger">
                            {% if overdue_tasks > 0 %}
                                تحتاج متابعة عاجلة
                            {% else %}
                                جميع المهام في الوقت المحدد
                            {% endif %}
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle fa-2x text-white"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tasks:list' %}?overdue_only=true" class="btn btn-sm btn-outline-danger w-100">
                        <i class="fas fa-eye me-1"></i> عرض المتأخرة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Overview -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2 text-primary"></i>
                    نظرة عامة على الأداء
                </h5>
                <div class="completion-rate">{{ completion_rate }}%</div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="progress mb-3" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar"
                                 style="width: {{ completion_rate }}%"
                                 aria-valuenow="{{ completion_rate }}"
                                 aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <p class="text-muted mb-0">معدل إنجاز المهام</p>
                    </div>
                    <div class="col-md-6">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="h4 mb-0 text-success">{{ completed_tasks }}</div>
                                <small class="text-muted">مكتملة</small>
                            </div>
                            <div class="col-4">
                                <div class="h4 mb-0 text-info">{{ in_progress_tasks }}</div>
                                <small class="text-muted">جارية</small>
                            </div>
                            <div class="col-4">
                                <div class="h4 mb-0 text-danger">{{ overdue_tasks }}</div>
                                <small class="text-muted">متأخرة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-flag me-2 text-warning"></i>
                    توزيع الأولويات
                </h5>
            </div>
            <div class="card-body">
                {% for priority in priority_stats %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="priority-badge
                        {% if priority.priority == 'urgent' %}bg-danger text-white
                        {% elif priority.priority == 'high' %}bg-warning text-dark
                        {% elif priority.priority == 'medium' %}bg-info text-white
                        {% else %}bg-secondary text-white{% endif %}">
                        {% if priority.priority == 'urgent' %}عاجلة
                        {% elif priority.priority == 'high' %}عالية
                        {% elif priority.priority == 'medium' %}متوسطة
                        {% else %}منخفضة{% endif %}
                    </span>
                    <span class="fw-bold">{{ priority.count }}</span>
                </div>
                {% empty %}
                <p class="text-muted text-center mb-0">لا توجد مهام</p>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- My Tasks -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">مهامي</h5>
                <a href="{% url 'tasks:my_tasks' %}" class="btn btn-sm btn-primary">عرض الكل</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>تاريخ الإنشاء</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الأولوية</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in my_tasks %}
                            <tr>
                                <td>
                                    <a href="{% url 'tasks:detail' task.id %}">{{ task.title }}</a>
                                </td>
                                <td>{{ task.created_at|date:"Y-m-d" }}</td>
                                <td>{{ task.due_date|date:"Y-m-d" }}</td>
                                <td>
                                    {% if task.priority == 'high' %}
                                    <span class="badge bg-danger">عالية</span>
                                    {% elif task.priority == 'medium' %}
                                    <span class="badge bg-warning">متوسطة</span>
                                    {% else %}
                                    <span class="badge bg-info">منخفضة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif task.status == 'in_progress' %}
                                    <span class="badge bg-info">قيد التنفيذ</span>
                                    {% elif task.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif task.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-4">لا توجد مهام مسندة إليك</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{% url 'tasks:create' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> إنشاء مهمة جديدة
                    </a>
                    <a href="{% url 'tasks:my_tasks' %}" class="btn btn-success">
                        <i class="fas fa-user-check me-2"></i> عرض مهامي
                    </a>
                    <a href="{% url 'tasks:reports' %}" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i> تقارير المهام
                    </a>
                </div>
            </div>
        </div>

        <!-- Overdue Tasks -->
        <div class="card mt-4">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-circle me-2"></i> المهام المتأخرة
                </h5>
            </div>
            <div class="card-body">
                {% if overdue_tasks_list %}
                <ul class="list-group list-group-flush">
                    {% for task in overdue_tasks_list %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>{{ task.title }}</span>
                        <span class="badge bg-danger rounded-pill">{{ task.due_date|date:"Y-m-d" }}</span>
                    </li>
                    {% endfor %}
                </ul>
                <div class="mt-3">
                    <a href="{% url 'tasks:list' %}?status=overdue" class="btn btn-danger w-100">
                        عرض جميع المهام المتأخرة
                    </a>
                </div>
                {% else %}
                <p class="text-center mb-0">لا توجد مهام متأخرة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
