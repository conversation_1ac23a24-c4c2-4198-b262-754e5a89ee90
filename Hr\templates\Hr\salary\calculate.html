{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}
{% load crispy_forms_tags %}

{% block title %}حساب الرواتب - نظام الدولية{% endblock %}

{% block page_title %}حساب الرواتب{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">حساب الرواتب</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3">
                <h3 class="card-title mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    حساب الرواتب الشهرية
                </h3>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم حساب الرواتب لجميع الموظفين النشطين بناءً على بنود الرواتب المعرفة لهم.
                    </div>

                    {{ form|crispy }}

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-calculator me-1"></i>
                            حساب الرواتب
                        </button>
                        <a href="{% url 'Hr:salaries:payroll_entry_list' %}" class="btn btn-secondary">
                            <i class="fas fa-list me-1"></i>
                            عرض سجلات الرواتب
                        </a>
                    </div>
                </form>
            </div>
        </div>

        {% if created_entries %}
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-light py-3">
                <h3 class="card-title mb-0">
                    <i class="fas fa-check-circle me-2 text-success"></i>
                    نتائج الحساب
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-check me-2"></i>
                    تم حساب الرواتب بنجاح لـ {{ created_entries|length }} موظف.
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>إجمالي الراتب</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in created_entries %}
                            <tr>
                                <td>{{ entry.employee.emp_full_name }}</td>
                                <td>{{ entry.total_amount }}</td>
                                <td>
                                    <span class="badge bg-warning">قيد المراجعة</span>
                                </td>
                                <td>
                                    <a href="{% url 'Hr:payroll_entry_detail' entry.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                        عرض التفاصيل
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize date picker for period field
    $('input[type="date"]').flatpickr({
        dateFormat: "Y-m",
        plugins: [
            new monthSelectPlugin({
                shorthand: true,
                dateFormat: "Y-m",
                altFormat: "F Y"
            })
        ]
    });
});
</script>
{% endblock %}