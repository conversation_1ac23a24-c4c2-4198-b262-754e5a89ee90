{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}

{% block title %}
{% if is_edit %}تعديل قسم - {{ form.instance.dept_name }}{% else %}إضافة قسم جديد{% endif %} - نظام الدولية
{% endblock %}

{% block page_title %}
{% if is_edit %}تعديل قسم - {{ form.instance.dept_name }}{% else %}إضافة قسم جديد{% endif %}
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:departments:department_list' %}">الأقسام</a></li>
<li class="breadcrumb-item active">{% if is_edit %}تعديل {% else %}إضافة{% endif %} قسم</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-{% if is_edit %}edit{% else %}plus-circle{% endif %} text-primary me-2"></i>
                    {% if is_edit %}تعديل بيانات قسم{% else %}إضافة قسم جديد{% endif %}
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="post" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger mb-4">
                        {% for error in form.non_field_errors %}
                        <p class="mb-0">{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row g-3">
                        <!-- Department Code -->
                        <div class="col-md-6">
                            <label for="{{ form.dept_code.id_for_label }}" class="form-label">كود القسم <span class="text-danger">*</span></label>
                            {{ form.dept_code }}
                            {% if form.dept_code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.dept_code.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.dept_code.help_text %}
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>{{ form.dept_code.help_text }}
                            </div>
                            {% else %}
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>سيتم إنشاء كود القسم تلقائيًا عند الحفظ
                            </div>
                            {% endif %}
                        </div>

                        <!-- Department Name -->
                        <div class="col-md-6">
                            <label for="{{ form.dept_name.id_for_label }}" class="form-label">اسم القسم <span class="text-danger">*</span></label>
                            {{ form.dept_name }}
                            {% if form.dept_name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.dept_name.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.dept_name.help_text %}
                            <div class="form-text">{{ form.dept_name.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Department Manager -->
                        <div class="col-md-12">
                            <label for="{{ form.manager.id_for_label }}" class="form-label">مدير القسم</label>
                            <div class="select2-container-wrapper">
                                {{ form.manager }}
                            </div>
                            {% if form.manager.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.manager.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.manager.help_text %}
                            <div class="form-text">{{ form.manager.help_text }}</div>
                            {% else %}
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>يمكنك البحث عن الموظف بالاسم
                            </div>
                            {% endif %}
                        </div>

                        <!-- Department Status -->
                        <div class="col-md-12 mt-3">
                            <label class="form-label d-block">الحالة</label>
                            <div class="d-flex">
                                {% for radio in form.is_active %}
                                <div class="form-check form-check-inline">
                                    {{ radio.tag }}
                                    <label class="form-check-label" for="{{ radio.id_for_label }}">
                                        {{ radio.choice_label }}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            {% if form.is_active.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.is_active.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.is_active.help_text %}
                            <div class="form-text">{{ form.is_active.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Department Notes -->
                        <div class="col-md-12 mt-3">
                            <label for="{{ form.note.id_for_label }}" class="form-label">ملاحظات</label>
                            {{ form.note }}
                            {% if form.note.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.note.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.note.help_text %}
                            <div class="form-text">{{ form.note.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Submit Button Section -->
                        <div class="col-12 mt-4">
                            <hr>
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="{% url 'Hr:departments:department_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right me-1"></i> عودة
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-{% if is_edit %}save{% else %}plus-circle{% endif %} me-1"></i>
                                    {% if is_edit %}حفظ التغييرات{% else %}إضافة قسم{% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        {% if is_edit %}
        <!-- Department Employees Card -->
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-users text-primary me-2"></i>موظفو القسم
                    <span class="badge bg-primary rounded-pill ms-2">{{ employee_count }}</span>
                </h5>
                <a href="{% url 'Hr:employees:create' %}?department={{ form.instance.dept_code }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-user-plus me-1"></i> إضافة موظف
                </a>
            </div>
            <div class="card-body p-0">
                {% if employee_list %}
                <div class="list-group list-group-flush">
                    {% for employee in employee_list %}
                    <div class="list-group-item">
                        <div class="d-flex align-items-center">
                            {% if employee.emp_image %}
                            <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}" class="rounded-circle me-3 object-fit-cover" width="50" height="50">
                            {% else %}
                            <div class="avatar-sm bg-secondary text-white me-3">{{ employee.emp_first_name|slice:":1"|upper }}</div>
                            {% endif %}

                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-1">{{ employee.emp_full_name }}</h6>
                                    {% if employee.working_condition == 'سارى' %}
                                    <span class="badge bg-success-subtle text-success rounded-pill">سارى</span>
                                    {% elif employee.working_condition == 'إجازة' %}
                                    <span class="badge bg-info-subtle text-info rounded-pill">إجازة</span>
                                    {% elif employee.working_condition == 'استقالة' %}
                                    <span class="badge bg-danger-subtle text-danger rounded-pill">استقالة</span>
                                    {% else %}
                                    <span class="badge bg-secondary-subtle text-secondary rounded-pill">{{ employee.working_condition|default:"-" }}</span>
                                    {% endif %}
                                </div>
                                <p class="text-muted small mb-0">{{ employee.jop_name|default:"-" }}</p>
                            </div>

                            <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="btn btn-sm btn-outline-primary ms-2">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                {% if employee_list|length > 5 %}
                <div class="text-center p-3 border-top">
                    <a href="{% url 'Hr:employees:list' %}?department={{ form.instance.dept_code }}" class="btn btn-link text-decoration-none">
                        عرض كل الموظفين ({{ employee_count }})
                    </a>
                </div>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x mb-3 text-muted"></i>
                    <p class="mb-0">لا يوجد موظفين مسجلين في هذا القسم حالياً.</p>
                    <a href="{% url 'Hr:employees:create' %}?department={{ form.instance.dept_code }}" class="btn btn-primary mt-3">
                        <i class="fas fa-user-plus me-1"></i> إضافة موظف جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-sm {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 1.25rem;
        font-weight: bold;
    }

    .object-fit-cover {
        object-fit: cover;
    }

    /* تحسين مظهر حقل Select2 */
    .select2-container-wrapper {
        width: 100%;
    }

    .select2-container--bootstrap-5 {
        width: 100% !important;
    }

    .select2-container--bootstrap-5 .select2-selection {
        height: calc(1.5em + 0.75rem + 2px);
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }

    .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
        padding-right: 0;
        padding-left: 0;
        color: #212529;
    }

    .select2-container--bootstrap-5 .select2-dropdown {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
        background-color: #0d6efd;
        color: white;
    }

    /* تحسين مظهر حقل البحث في Select2 */
    .select2-search--dropdown .select2-search__field {
        padding: 0.5rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        width: 100%;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تنفيذ الكود بعد تحميل الصفحة بالكامل
    $(document).ready(function() {
        console.log("Document ready, initializing form...");

        // Add custom class to form inputs
        document.querySelectorAll('form select, form input, form textarea').forEach(function(element) {
            // Skip checkboxes and radio buttons
            if (element.type !== 'checkbox' && element.type !== 'radio') {
                element.classList.add('form-control');

                // Add is-invalid class to inputs with errors
                if (element.closest('.col-md-6, .col-md-12')?.querySelector('.invalid-feedback')) {
                    element.classList.add('is-invalid');
                }
            } else {
                // For checkboxes and radio buttons add form-check-input class
                element.classList.add('form-check-input');
            }
        });

        // تعيين حقل كود القسم كقراءة فقط
        const deptCodeField = document.getElementById('{{ form.dept_code.id_for_label }}');
        if (deptCodeField) {
            deptCodeField.setAttribute('readonly', 'readonly');
            deptCodeField.setAttribute('placeholder', 'سيتم إنشاؤه تلقائيًا');
        }

        // تهيئة حقل مدير القسم باستخدام Select2
        try {
            console.log("Initializing Select2 for manager field...");

            // التأكد من وجود حقل مدير القسم
            const managerField = $('#{{ form.manager.id_for_label }}');
            if (managerField.length) {
                console.log("Manager field found, applying Select2...");

                // تطبيق Select2 على حقل مدير القسم
                managerField.select2({
                    theme: 'bootstrap-5',
                    placeholder: 'اختر مدير القسم',
                    allowClear: true,
                    width: '100%',
                    dir: 'rtl',
                    language: {
                        noResults: function() {
                            return 'لا توجد نتائج مطابقة';
                        },
                        searching: function() {
                            return 'جاري البحث...';
                        }
                    }
                });

                console.log("Select2 applied successfully to manager field");
            } else {
                console.error("Manager field not found!");
            }
        } catch (error) {
            console.error("Error initializing Select2:", error);
        }
    });
</script>
{% endblock %}
