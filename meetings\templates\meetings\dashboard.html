{% extends "meetings/base_meetings.html" %}
{% load static %}

{% block title %}لوحة تحكم الاجتماعات{% endblock %}

{% block page_title %}لوحة تحكم الاجتماعات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">لوحة تحكم الاجتماعات</li>
{% endblock %}

{% block content %}
<div class="section-spacing">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-header-content">
            <div class="page-title">
                <i class="fas fa-calendar-alt"></i>
                <span>لوحة تحكم الاجتماعات</span>
            </div>
            <div class="page-subtitle">إدارة الاجتماعات والمواعيد</div>
        </div>
        <div class="page-actions">
            <a href="{% url 'meetings:meeting_create' %}" class="btn btn-primary btn-with-icon">
                <i class="fas fa-plus"></i>
                <span>اجتماع جديد</span>
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-icon">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">إجمالي الاجتماعات</div>
                <div class="stats-card-value">{{ total_meetings }}</div>
            </div>
        </div>

        <div class="stats-card stats-card-info">
            <div class="stats-card-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">اجتماعات اليوم</div>
                <div class="stats-card-value">{{ today_meetings }}</div>
            </div>
        </div>

        <div class="stats-card stats-card-success">
            <div class="stats-card-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">اجتماعات مكتملة</div>
                <div class="stats-card-value">{{ completed_meetings }}</div>
            </div>
        </div>

        <div class="stats-card stats-card-warning">
            <div class="stats-card-icon">
                <i class="fas fa-calendar-times"></i>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">اجتماعات قادمة</div>
                <div class="stats-card-value">{{ upcoming_meetings }}</div>
            </div>
        </div>
    </div>
    
    .stat-info span {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .meeting-list-item {
        border-right: 3px solid var(--primary-color);
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        transition: all 0.2s ease;
    }
    
    .meeting-list-item:hover {
        background-color: rgba(52, 152, 219, 0.05);
        transform: translateX(-5px);
    }
    
    .meeting-list-item h6 {
        margin-bottom: 0.25rem;
        font-weight: 600;
    }
    
    .meeting-list-item .meeting-time {
        display: inline-block;
        background: var(--light-color);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }
    
    .meeting-list-item .badge {
        font-size: 0.7rem;
        padding: 0.3rem 0.5rem;
    }
    
    .status-pending {
        color: #ff9800;
    }
    
    .status-completed {
        color: #4caf50;
    }
    
    .status-cancelled {
        color: #f44336;
    }
    
    .dashboard-section {
        margin-bottom: 2rem;
    }
    
    .dashboard-section-title {
        margin-bottom: 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-weight: 600;
    }

    /* Chart Container */
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Stats Row -->
    <div class="row dashboard-section">
        <!-- Total Meetings -->
        <div class="col-md-6 col-lg-3 mb-4">
            <a href="{% url 'meetings:list' %}" class="text-decoration-none">
                <div class="stat-card">
                    <div class="stat-icon bg-primary text-white">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h4>{{ total_meetings }}</h4>
                        <span>إجمالي الاجتماعات</span>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Today's Meetings -->
        <div class="col-md-6 col-lg-3 mb-4">
            <a href="{% url 'meetings:list' %}?date_from={{ today|date:'Y-m-d' }}&date_to={{ today|date:'Y-m-d' }}" class="text-decoration-none">
                <div class="stat-card">
                    <div class="stat-icon bg-success text-white">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-info">
                        <h4>{{ today_meetings }}</h4>
                        <span>اجتماعات اليوم</span>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Week Meetings -->
        <div class="col-md-6 col-lg-3 mb-4">
            <a href="{% url 'meetings:list' %}?date_from={{ today|date:'Y-m-d' }}&date_to={{ today|add:'6 days'|date:'Y-m-d' }}" class="text-decoration-none">
                <div class="stat-card">
                    <div class="stat-icon bg-info text-white">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="stat-info">
                        <h4>{{ week_meetings }}</h4>
                        <span>اجتماعات الأسبوع</span>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Completed Meetings -->
        <div class="col-md-6 col-lg-3 mb-4">
            <a href="{% url 'meetings:list' %}?status=completed" class="text-decoration-none">
                <div class="stat-card">
                    <div class="stat-icon bg-warning text-white">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h4>{{ completed_meetings }}</h4>
                        <span>الاجتماعات المكتملة</span>
                    </div>
                </div>
            </a>
        </div>
    </div>
    
    <!-- Content Rows -->
    <div class="row">
        <!-- Today's Meetings List -->
        <div class="col-md-6 mb-4">
            <div class="card dashboard-card h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-day me-2 text-primary"></i> اجتماعات اليوم</h5>
                </div>
                <div class="card-body">
                    {% if today_meetings_list %}
                        {% for meeting in today_meetings_list %}
                            <div class="meeting-list-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6><a href="{% url 'meetings:detail' pk=meeting.pk %}">{{ meeting.title }}</a></h6>
                                        <small class="text-muted">{{ meeting.date|date:"h:i A" }}</small>
                                    </div>
                                    <span class="badge bg-{{ meeting.status|cut:'pending'|cut:'completed'|cut:'cancelled'|yesno:'warning,success,danger' }}">
                                        {{ meeting.get_status_display }}
                                    </span>
                                </div>
                            </div>
                        {% endfor %}
                        {% if today_meetings > 5 %}
                            <div class="text-center mt-3">
                                <a href="{% url 'meetings:list' %}?date_from={{ today|date:'Y-m-d' }}&date_to={{ today|date:'Y-m-d' }}" class="btn btn-sm btn-outline-primary">
                                    عرض كل اجتماعات اليوم ({{ today_meetings }})
                                </a>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <img src="{% static 'img/no-events.svg' %}" alt="لا يوجد اجتماعات" style="max-width: 150px; opacity: 0.7;">
                            <p class="text-muted mt-3">لا توجد اجتماعات مجدولة لهذا اليوم</p>
                            <a href="{% url 'meetings:create' %}" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-plus-circle me-1"></i> إنشاء اجتماع جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Upcoming Meetings -->
        <div class="col-md-6 mb-4">
            <div class="card dashboard-card h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-plus me-2 text-primary"></i> الاجتماعات القادمة ({{ upcoming_meetings_count }})</h5>
                </div>
                <div class="card-body">
                    {% if upcoming_meetings %}
                        {% for meeting in upcoming_meetings %}
                            <div class="meeting-list-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6><a href="{% url 'meetings:detail' pk=meeting.pk %}">{{ meeting.title }}</a></h6>
                                        <small class="text-muted">{{ meeting.date|date:"Y-m-d h:i A" }}</small>
                                    </div>
                                    <span class="badge bg-{{ meeting.status|cut:'pending'|cut:'completed'|cut:'cancelled'|yesno:'warning,success,danger' }}">
                                        {{ meeting.get_status_display }}
                                    </span>
                                </div>
                            </div>
                        {% endfor %}
                        <div class="text-center mt-3">
                            <a href="{% url 'meetings:calendar' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-calendar me-1"></i> عرض التقويم الكامل
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <img src="{% static 'img/no-events.svg' %}" alt="لا يوجد اجتماعات" style="max-width: 150px; opacity: 0.7;">
                            <p class="text-muted mt-3">لا توجد اجتماعات قادمة</p>
                            <a href="{% url 'meetings:create' %}" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-plus-circle me-1"></i> إنشاء اجتماع جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Row -->
    <div class="row">
        <!-- Meeting Status Chart -->
        <div class="col-md-6 mb-4">
            <div class="card dashboard-card h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2 text-primary"></i> توزيع حالة الاجتماعات</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="meetingStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="col-md-6 mb-4">
            <div class="card dashboard-card h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2 text-primary"></i> إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <a href="{% url 'meetings:create' %}" class="btn btn-primary btn-lg w-100 d-flex align-items-center justify-content-center gap-2">
                                <i class="fas fa-plus-circle"></i>
                                <span>اجتماع جديد</span>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{% url 'meetings:list' %}" class="btn btn-outline-primary btn-lg w-100 d-flex align-items-center justify-content-center gap-2">
                                <i class="fas fa-list"></i>
                                <span>قائمة الاجتماعات</span>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{% url 'meetings:calendar' %}" class="btn btn-outline-primary btn-lg w-100 d-flex align-items-center justify-content-center gap-2">
                                <i class="fas fa-calendar"></i>
                                <span>التقويم</span>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{% url 'meetings:reports' %}" class="btn btn-outline-primary btn-lg w-100 d-flex align-items-center justify-content-center gap-2">
                                <i class="fas fa-chart-bar"></i>
                                <span>التقارير</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Meeting Status Chart
        const statusCtx = document.getElementById('meetingStatusChart').getContext('2d');
        const pendingCount = {{ total_meetings }} - {{ completed_meetings }};
        
        const meetingStatusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['قيد الانتظار', 'مكتملة', 'ملغاة'],
                datasets: [{
                    data: [pendingCount, {{ completed_meetings }}, {{ total_meetings }} - pendingCount - {{ completed_meetings }}],
                    backgroundColor: [
                        '#ff9800',
                        '#4caf50',
                        '#f44336'
                    ],
                    borderColor: [
                        '#fff',
                        '#fff',
                        '#fff'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 12,
                                family: 'Cairo'
                            }
                        }
                    }
                },
                cutout: '65%'
            }
        });
    });
</script>
{% endblock %}
