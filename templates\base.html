{% load static %}
<!DOCTYPE html>
<html lang="{{ current_language }}" dir="{{ text_direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ElDawliya System{% endblock %}</title>
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- ElDawliya Design System -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="stylesheet" href="{% static 'css/theme-toggle.css' %}">
    <!-- RTL CSS -->
    <link rel="stylesheet" href="{% static 'css/rtl.css' %}">

    <!-- Theme Detection Script (Inline for immediate execution) -->
    <script>
        // Prevent flash of unstyled content
        (function() {
            const theme = localStorage.getItem('eldawliya-theme') ||
                         (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
            document.documentElement.setAttribute('data-theme', theme);
        })();
    </script>
    <!-- Google Fonts -->
    {% if system_settings.font_family == 'cairo' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'tajawal' %}
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'almarai' %}
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'ibm-plex-sans-arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'noto-sans-arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% else %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% endif %}
    
    <style>
        :root {
            --font-family: 
                {% if system_settings.font_family == 'cairo' %}'Cairo'
                {% elif system_settings.font_family == 'tajawal' %}'Tajawal'
                {% elif system_settings.font_family == 'almarai' %}'Almarai'
                {% elif system_settings.font_family == 'ibm-plex-sans-arabic' %}'IBM Plex Sans Arabic'
                {% elif system_settings.font_family == 'noto-sans-arabic' %}'Noto Sans Arabic'
                {% else %}'Cairo'{% endif %}, sans-serif;
        }

        body {
            font-family: var(--font-family), system-ui, -apple-system, sans-serif;
            text-align: right;
        }

        /* RTL specific styles */
        .dropdown-menu {
            text-align: right;
        }

        .form-check {
            padding-right: 1.5em;
            padding-left: 0;
        }

        .form-check .form-check-input {
            float: right;
            margin-right: -1.5em;
            margin-left: 0;
        }

        .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
            margin-right: -1px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Enhanced Modern Navigation Bar -->
    <nav class="navbar navbar-expand-lg" role="navigation" aria-label="التنقل الرئيسي">
        <div class="container-fluid">
            <!-- Brand Section -->
            <a class="navbar-brand" href="{% url 'home_dashboard' %}" aria-label="الصفحة الرئيسية">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-building" aria-hidden="true"></i>
                </div>
                <div class="navbar-brand-text">
                    <div class="sidebar-brand-title">نظام الدولية</div>
                    <div class="sidebar-brand-subtitle">إدارة متكاملة</div>
                </div>
            </a>

            <!-- Mobile Navigation Toggle -->
            <button class="navbar-toggler" type="button" aria-controls="navbarNav" aria-expanded="false" aria-label="تبديل التنقل">
                <span class="navbar-toggler-icon" aria-hidden="true">
                    <i class="fas fa-bars"></i>
                </span>
            </button>

            <!-- Navigation Menu -->
            <ul class="navbar-nav" id="navbarNav" role="menubar">
                <!-- Quick Access Links -->
                <li class="nav-item" role="none">
                    <a class="nav-link" href="{% url 'home_dashboard' %}" role="menuitem">
                        <i class="nav-link-icon fas fa-home" aria-hidden="true"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item" role="none">
                    <a class="nav-link" href="{% url 'meetings:list' %}" role="menuitem">
                        <i class="nav-link-icon fas fa-users" aria-hidden="true"></i>
                        <span>الاجتماعات</span>
                    </a>
                </li>
                <li class="nav-item" role="none">
                    <a class="nav-link" href="{% url 'tasks:list' %}" role="menuitem">
                        <i class="nav-link-icon fas fa-tasks" aria-hidden="true"></i>
                        <span>المهام</span>
                    </a>
                </li>

                <!-- Notifications -->
                {% if user.is_authenticated %}
                <li class="nav-item" role="none">
                    <a class="nav-link position-relative" href="{% url 'notifications:user_notifications' %}" role="menuitem" aria-label="التنبيهات">
                        <i class="nav-link-icon fas fa-bell" aria-hidden="true"></i>
                        <span>التنبيهات</span>
                        <!-- Notification badge (if needed) -->
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem; display: none;">
                            <span class="visually-hidden">تنبيهات جديدة</span>
                        </span>
                    </a>
                </li>
                {% endif %}

                <!-- Global Search -->
                {% if user.is_authenticated %}
                <li class="nav-item" role="none">
                    <button class="nav-link global-search-trigger" type="button" aria-label="البحث العام" title="البحث في جميع أنحاء النظام (Ctrl+K)">
                        <i class="nav-link-icon fas fa-search" aria-hidden="true"></i>
                        <span>بحث</span>
                    </button>
                </li>
                {% endif %}

                <!-- User Dropdown -->
                {% if user.is_authenticated %}
                <li class="nav-item dropdown" role="none">
                    <a class="nav-link dropdown-toggle" href="#" role="menuitem" aria-haspopup="true" aria-expanded="false" aria-label="قائمة المستخدم">
                        <i class="nav-link-icon fas fa-user" aria-hidden="true"></i>
                        <span>{{ user.get_full_name|default:user.username }}</span>
                    </a>
                    <ul class="dropdown-menu" role="menu" aria-hidden="true">
                        <li role="none">
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="dropdown-item-icon fas fa-user-cog" aria-hidden="true"></i>
                                <span>الملف الشخصي</span>
                            </a>
                        </li>
                        <li role="none">
                            <a class="dropdown-item" href="#" role="menuitem">
                                <i class="dropdown-item-icon fas fa-cog" aria-hidden="true"></i>
                                <span>الإعدادات</span>
                            </a>
                        </li>
                        <li role="none">
                            <hr class="dropdown-divider">
                        </li>
                        <li role="none">
                            <a class="dropdown-item" href="{% url 'accounts:logout' %}" role="menuitem">
                                <i class="dropdown-item-icon fas fa-sign-out-alt" aria-hidden="true"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </li>
                    </ul>
                </li>
                {% else %}
                <li class="nav-item" role="none">
                    <a class="nav-link" href="{% url 'accounts:login' %}" role="menuitem">
                        <i class="nav-link-icon fas fa-sign-in-alt" aria-hidden="true"></i>
                        <span>تسجيل الدخول</span>
                    </a>
                </li>
                {% endif %}

                <!-- Theme Toggle -->
                <li class="nav-item" role="none">
                    <button class="nav-link theme-toggle-btn" type="button" aria-label="تبديل المظهر" title="تبديل بين المظهر الفاتح والداكن">
                        <i class="nav-link-icon fas fa-moon theme-icon" aria-hidden="true"></i>
                        <span class="theme-text">المظهر الداكن</span>
                    </button>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Navigation Overlay for Mobile -->
    <div class="navbar-overlay" aria-hidden="true"></div>

    <!-- Breadcrumb Navigation -->
    {% block breadcrumb %}
        {% if breadcrumb_items or page_title %}
            {% include 'components/breadcrumb.html' %}
        {% endif %}
    {% endblock %}

    <!-- Enhanced Messages -->
    <div class="container-fluid">
        {% if messages %}
            <div class="row">
                <div class="col-12">
                    {% for message in messages %}
                        <div class="alert alert-{% if message.tags == 'error' %}error{% elif message.tags == 'warning' %}warning{% elif message.tags == 'success' %}success{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                            <div class="alert-icon">
                                {% if message.tags == 'error' %}
                                    <i class="fas fa-exclamation-circle"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% elif message.tags == 'success' %}
                                    <i class="fas fa-check-circle"></i>
                                {% else %}
                                    <i class="fas fa-info-circle"></i>
                                {% endif %}
                            </div>
                            <div class="alert-content">
                                <div class="alert-title">
                                    {% if message.tags == 'error' %}خطأ
                                    {% elif message.tags == 'warning' %}تحذير
                                    {% elif message.tags == 'success' %}نجح
                                    {% else %}معلومات
                                    {% endif %}
                                </div>
                                <div class="alert-message">{{ message }}</div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Main Content -->
    <main class="main-content" role="main">
        <div class="container-fluid">
            {% block content %}
            {% endblock %}
        </div>
    </main>

    <!-- Modern Footer -->
    <footer class="mt-8 py-6" style="background: linear-gradient(135deg, var(--secondary-800) 0%, var(--secondary-900) 100%); color: var(--white);">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-3">
                        <div class="sidebar-brand-icon me-3">
                            <i class="fas fa-building"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 text-white">نظام الدولية إنترناشونال</h5>
                            <p class="mb-0 text-sm opacity-75">حلول إدارية متكاملة</p>
                        </div>
                    </div>
                    <p class="text-sm opacity-75">&copy; {{ "now"|date:"Y" }} نظام الدولية إنترناشونال للطباعة. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="mb-3">
                        <a href="#" class="text-white text-decoration-none me-4 hover:text-primary-300">
                            <i class="fas fa-shield-alt me-1"></i>سياسة الخصوصية
                        </a>
                        <a href="#" class="text-white text-decoration-none hover:text-primary-300">
                            <i class="fas fa-file-contract me-1"></i>شروط الاستخدام
                        </a>
                    </div>
                    <p class="text-sm opacity-75">
                        مدعوم بواسطة <a href="https://www.eldawliya.com" class="text-primary-300 text-decoration-none font-medium">ElDawliya</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastify JS -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- Enhanced Navigation System -->
    <script src="{% static 'js/navigation.js' %}"></script>
    <!-- Component Library -->
    <script src="{% static 'js/components.js' %}"></script>
    <!-- Global Search System -->
    <script src="{% static 'js/global-search.js' %}"></script>
    <!-- ElDawliya Theme System -->
    <script src="{% static 'js/theme-toggle.js' %}"></script>
    <!-- Custom JavaScript -->
    <script>
        // Apply RTL specific JavaScript adjustments
        document.addEventListener('DOMContentLoaded', function() {
            // Set RTL for all elements that need it
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.textAlign = 'right';
            });

            // Adjust data tables for RTL if you're using them
            if (typeof $.fn.DataTable !== 'undefined') {
                $.extend(true, $.fn.DataTable.defaults, {
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
                    }
                });
            }

            // Fix any RTL issues with third-party plugins
            document.querySelectorAll('.fc-header-toolbar').forEach(toolbar => {
                toolbar.style.direction = 'rtl';
            });

            // Initialize tooltips with RTL support
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            if (tooltipTriggerList.length > 0) {
                tooltipTriggerList.forEach(tooltipTriggerEl => {
                    new bootstrap.Tooltip(tooltipTriggerEl, {
                        placement: 'auto',
                        container: 'body'
                    });
                });
            }
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
