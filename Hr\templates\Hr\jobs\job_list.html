{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}قائمة الوظائف - نظام الدولية{% endblock %}

{% block page_title %}قائمة الوظائف{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">قائمة الوظائف</li>
{% endblock %}

{% block content %}
<!-- Search and Filter Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-briefcase text-primary me-2"></i>قائمة الوظائف
                </h5>
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">{{ title }}</h4>
                    {% if perms.Hr.add_job or user|is_admin %}
                    <a href="{% url 'Hr:jobs:job_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إضافة وظيفة جديدة
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 mb-3 mb-md-0">
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text" id="jobSearch" class="form-control" placeholder="ابحث عن وظيفة (بالاسم أو الكود أو القسم)..." aria-label="بحث">
                            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select id="jobSort" class="form-select">
                            <option value="name">ترتيب حسب الاسم</option>
                            <option value="code">ترتيب حسب الكود</option>
                            <option value="department">ترتيب حسب القسم</option>
                            <option value="employees">ترتيب حسب عدد الموظفين</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Cards View -->
<div class="row g-4" id="jobsContainer">
    {% if jobs %}
        {% for job in jobs %}
        <div class="col-lg-4 col-md-6 job-card"
             data-name="{{ job.jop_name }}"
             data-code="{{ job.jop_code }}"
             data-department="{{ job.department.dept_name|default:'غير محدد' }}"
             data-employees="{{ job.employee_count }}">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h5 class="card-title">{{ job.jop_name }}</h5>
                        <span class="badge bg-info rounded-pill">{{ job.employee_count }} موظف</span>
                    </div>

                    <div class="job-department d-flex align-items-center mb-3 pb-3 border-bottom">
                        <div class="department-icon bg-primary-subtle text-primary rounded-circle me-3 flex-shrink-0">
                            <i class="fas fa-building"></i>
                        </div>
                        <div>
                            <span class="d-block fw-medium">{{ job.department.dept_name|default:"غير محدد" }}</span>
                            <small class="text-muted">القسم</small>
                        </div>
                    </div>

                    <div class="job-code mb-4">
                        <div class="d-flex align-items-center">
                            <div class="code-icon bg-secondary-subtle text-secondary rounded-circle me-3 flex-shrink-0">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <div>
                                <span class="d-block fw-medium">{{ job.jop_code }}</span>
                                <small class="text-muted">كود الوظيفة</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end align-items-center mt-auto">
                        <div class="btn-group btn-group-sm">
                            <a href="{% url 'Hr:jobs:job_detail' job.jop_code %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if perms.Hr.change_job or user|is_admin %}
                            <a href="{% url 'Hr:jobs:job_edit' job.jop_code %}" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            {% endif %}
                            {% if perms.Hr.delete_job or user|is_admin %}
                            <button type="button" class="btn btn-sm btn-danger delete-job"
                                    data-job-id="{{ job.jop_code }}"
                                    data-job-name="{{ job.jop_name }}">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-briefcase fa-3x mb-3 text-muted"></i>
                <p class="mb-0">لا توجد وظائف مسجلة حالياً.</p>
                {% has_hr_module_permission "jobs" "add" as job_perm %}
                {% if perms|has_perm:job_perm or user|is_admin %}
                <a href="{% url 'Hr:jobs:job_create' %}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus-circle me-1"></i> إضافة وظيفة جديدة
                </a>
                {% endif %}
            </div>
        </div>
    {% endif %}

    <!-- No Results Message -->
    <div id="noResults" class="col-12 d-none">
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x mb-3 text-muted"></i>
            <p class="mb-0">لا توجد نتائج مطابقة لبحثك.</p>
            <button id="resetSearch" class="btn btn-outline-primary mt-3">
                <i class="fas fa-redo me-1"></i> عرض جميع الوظائف
            </button>
        </div>
    </div>

    <!-- Add New Job Card -->
    {% has_hr_module_permission "jobs" "add" as job_perm %}
    {% if perms|has_perm:job_perm or user|is_admin %}
    <div class="col-lg-4 col-md-6" id="addJobCard">
        <div class="card h-100 border-dashed border-primary-subtle">
            <div class="card-body d-flex flex-column justify-content-center align-items-center h-100 py-5">
                <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                <h5 class="card-title text-center">إضافة وظيفة جديدة</h5>
                <p class="card-text text-center text-muted mb-4">قم بإضافة وظيفة جديدة إلى الهيكل الوظيفي للشركة</p>
                <a href="{% url 'Hr:jobs:job_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-1"></i> إضافة وظيفة
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الوظيفة: <span id="itemName" class="fw-bold"></span>؟</p>
                <p class="text-danger mb-0"><i class="fas fa-exclamation-triangle me-1"></i> هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDelete" class="btn btn-danger">تأكيد الحذف</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Card Styles */
    .border-dashed {
        border-style: dashed !important;
        border-width: 2px !important;
    }

    .card {
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    /* Icon Styles */
    .department-icon, .code-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 1rem;
    }

    /* Search and Filter Styles */
    #jobSearch:focus {
        box-shadow: none;
        border-color: #80bdff;
    }

    #clearSearch {
        cursor: pointer;
    }

    #jobSort {
        cursor: pointer;
    }

    /* Animation for cards */
    .job-card {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* No results message */
    #noResults {
        animation: fadeIn 0.5s ease-in-out;
    }

    /* Job department section */
    .job-department {
        background-color: rgba(0, 0, 0, 0.01);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle delete modal
        const deleteModal = document.getElementById('deleteModal');
        if (deleteModal) {
            deleteModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const itemName = button.getAttribute('data-name');
                const url = button.getAttribute('data-url');

                document.getElementById('itemName').textContent = itemName;
                document.getElementById('confirmDelete').setAttribute('href', url);
            });
        }

        // Search and filter functionality
        const searchInput = document.getElementById('jobSearch');
        const sortSelect = document.getElementById('jobSort');
        const clearSearchBtn = document.getElementById('clearSearch');
        const resetSearchBtn = document.getElementById('resetSearch');
        const jobCards = document.querySelectorAll('.job-card');
        const noResultsDiv = document.getElementById('noResults');
        const addJobCard = document.getElementById('addJobCard');

        // Function to filter jobs
        function filterJobs() {
            const searchTerm = searchInput.value.trim().toLowerCase();
            let visibleCount = 0;

            jobCards.forEach(card => {
                const jobName = card.getAttribute('data-name').toLowerCase();
                const jobCode = card.getAttribute('data-code').toLowerCase();
                const jobDepartment = card.getAttribute('data-department').toLowerCase();

                if (jobName.includes(searchTerm) ||
                    jobCode.includes(searchTerm) ||
                    jobDepartment.includes(searchTerm)) {
                    card.classList.remove('d-none');
                    visibleCount++;
                } else {
                    card.classList.add('d-none');
                }
            });

            // Show/hide no results message
            if (visibleCount === 0 && searchTerm !== '') {
                noResultsDiv.classList.remove('d-none');
                if (addJobCard) {
                    addJobCard.classList.add('d-none');
                }
            } else {
                noResultsDiv.classList.add('d-none');
                if (addJobCard) {
                    addJobCard.classList.remove('d-none');
                }
            }
        }

        // Function to sort jobs
        function sortJobs() {
            const sortBy = sortSelect.value;
            const container = document.getElementById('jobsContainer');
            const cards = Array.from(jobCards);

            cards.sort((a, b) => {
                if (sortBy === 'name') {
                    return a.getAttribute('data-name').localeCompare(b.getAttribute('data-name'));
                } else if (sortBy === 'code') {
                    return parseInt(a.getAttribute('data-code')) - parseInt(b.getAttribute('data-code'));
                } else if (sortBy === 'department') {
                    return a.getAttribute('data-department').localeCompare(b.getAttribute('data-department'));
                } else if (sortBy === 'employees') {
                    return parseInt(b.getAttribute('data-employees')) - parseInt(a.getAttribute('data-employees'));
                }
                return 0;
            });

            // Remove all cards from container
            jobCards.forEach(card => {
                card.remove();
            });

            // Add sorted cards back to container before the "Add Job" card
            if (addJobCard) {
                cards.forEach(card => {
                    container.insertBefore(card, addJobCard);
                });
            } else {
                cards.forEach(card => {
                    container.appendChild(card);
                });
            }
        }

        // Event listeners
        if (searchInput) {
            searchInput.addEventListener('input', filterJobs);
        }

        if (sortSelect) {
            sortSelect.addEventListener('change', sortJobs);
        }

        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', function() {
                searchInput.value = '';
                filterJobs();
            });
        }

        if (resetSearchBtn) {
            resetSearchBtn.addEventListener('click', function() {
                searchInput.value = '';
                filterJobs();
            });
        }
    });
</script>
{% endblock %}
