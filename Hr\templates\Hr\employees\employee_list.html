{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}
{% load form_utils %}
{% load django_permissions %}

{% block title %}قائمة الموظفين - نظام الدولية{% endblock %}

{% block page_title %}إدارة الموظفين{% endblock %}
{% block page_subtitle %}عرض وإدارة بيانات الموظفين في النظام{% endblock %}

{% block extra_css %}
<style>
/* Enhanced Employee List Styles */
.employee-list-container {
    --card-border-radius: 12px;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition-all: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    --gradient-warning: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
    --gradient-danger: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
}

/* Enhanced Page Header */
.modern-page-header {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 var(--card-border-radius) var(--card-border-radius);
    position: relative;
    overflow: hidden;
}

.modern-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    opacity: 0.1;
}

.page-header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.page-title-section {
    flex: 1;
    min-width: 300px;
}

.page-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.page-title-icon {
    font-size: 2rem;
    opacity: 0.9;
}

.page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    font-weight: 400;
}

.page-header-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* Enhanced Button Styles */
.btn-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: var(--transition-all);
    border: none;
    cursor: pointer;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern-primary {
    background: white;
    color: var(--primary-600);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-modern-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    color: var(--primary-700);
}

.btn-modern-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-modern-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
}

/* Responsive adjustments for header */
@media (max-width: 768px) {
    .modern-page-header {
        padding: 1.5rem 0;
        margin-bottom: 1.5rem;
    }

    .page-header-content {
        flex-direction: column;
        text-align: center;
    }

    .page-title-section {
        min-width: auto;
        width: 100%;
    }

    .page-title {
        font-size: 1.5rem;
        justify-content: center;
    }

    .page-header-actions {
        width: 100%;
        justify-content: center;
    }

    .btn-modern {
        flex: 1;
        justify-content: center;
        min-width: 140px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="employee-list-container">
    <!-- Enhanced Page Header -->
    <div class="modern-page-header">
        <div class="container-fluid">
            <div class="page-header-content">
                <div class="page-title-section">
                    <div class="page-title">
                        <i class="fas fa-users page-title-icon"></i>
                        <span>إدارة الموظفين</span>
                    </div>
                    <div class="page-subtitle">عرض وإدارة بيانات الموظفين في النظام</div>
                </div>
                <div class="page-header-actions">
                    {% if perms.Hr.add_employee or user|is_admin %}
                    <a href="{% url 'Hr:employees:create' %}" class="btn-modern btn-modern-primary">
                        <i class="fas fa-user-plus"></i>
                        <span>إضافة موظف جديد</span>
                    </a>
                    {% endif %}
                    <button class="btn-modern btn-modern-secondary" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i>
                        <span>تصدير Excel</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="container-fluid mb-4">
        <div class="row g-4">
            <!-- Total Employees Card -->
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="stats-card stats-card-primary">
                    <div class="stats-card-body">
                        <div class="stats-icon-container">
                            <div class="stats-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stats-trend stats-trend-up">
                                <i class="fas fa-arrow-up"></i>
                                <span>+5%</span>
                            </div>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" data-count="{{ total_employees }}">{{ total_employees }}</div>
                            <div class="stats-label">إجمالي الموظفين</div>
                            <div class="stats-description">في النظام</div>
                        </div>
                        <div class="stats-progress">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Employees Card -->
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="stats-card stats-card-success">
                    <div class="stats-card-body">
                        <div class="stats-icon-container">
                            <div class="stats-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stats-trend stats-trend-up">
                                <i class="fas fa-arrow-up"></i>
                                <span>+2%</span>
                            </div>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" data-count="{{ active_employees }}">{{ active_employees }}</div>
                            <div class="stats-label">الموظفين النشطين</div>
                            <div class="stats-description">{% widthratio active_employees total_employees 100 %}% من الإجمالي</div>
                        </div>
                        <div class="stats-progress">
                            <div class="progress-bar" style="width: {% widthratio active_employees total_employees 100 %}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- On Leave Employees Card -->
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="stats-card stats-card-warning">
                    <div class="stats-card-body">
                        <div class="stats-icon-container">
                            <div class="stats-icon">
                                <i class="fas fa-pause-circle"></i>
                            </div>
                            <div class="stats-trend stats-trend-neutral">
                                <i class="fas fa-minus"></i>
                                <span>0%</span>
                            </div>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" data-count="{{ on_leave_employees }}">{{ on_leave_employees }}</div>
                            <div class="stats-label">في إجازة</div>
                            <div class="stats-description">موظفين مؤقتاً</div>
                        </div>
                        <div class="stats-progress">
                            <div class="progress-bar" style="width: {% if total_employees %}{% widthratio on_leave_employees total_employees 100 %}{% else %}0{% endif %}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resigned Employees Card -->
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="stats-card stats-card-danger">
                    <div class="stats-card-body">
                        <div class="stats-icon-container">
                            <div class="stats-icon">
                                <i class="fas fa-user-times"></i>
                            </div>
                            <div class="stats-trend stats-trend-down">
                                <i class="fas fa-arrow-down"></i>
                                <span>-1%</span>
                            </div>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" data-count="{{ resigned_employees }}">{{ resigned_employees }}</div>
                            <div class="stats-label">المستقيلين</div>
                            <div class="stats-description">موظفين سابقين</div>
                        </div>
                        <div class="stats-progress">
                            <div class="progress-bar" style="width: {% if total_employees %}{% widthratio resigned_employees total_employees 100 %}{% else %}0{% endif %}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    /* Enhanced Statistics Cards */
    .stats-card {
        background: white;
        border-radius: var(--card-border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition-all);
        border: 1px solid rgba(0, 0, 0, 0.05);
        overflow: hidden;
        position: relative;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
        transition: var(--transition-all);
    }

    .stats-card-primary::before { background: var(--gradient-primary); }
    .stats-card-success::before { background: var(--gradient-success); }
    .stats-card-warning::before { background: var(--gradient-warning); }
    .stats-card-danger::before { background: var(--gradient-danger); }

    .stats-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--card-shadow-hover);
    }

    .stats-card-body {
        padding: 1.5rem;
        position: relative;
    }

    .stats-icon-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .stats-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        background: var(--primary-500);
    }

    .stats-card-primary .stats-icon { background: var(--primary-500); }
    .stats-card-success .stats-icon { background: var(--success-500); }
    .stats-card-warning .stats-icon { background: var(--warning-500); }
    .stats-card-danger .stats-icon { background: var(--danger-500); }

    .stats-trend {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .stats-trend-up {
        background: rgba(34, 197, 94, 0.1);
        color: var(--success-600);
    }

    .stats-trend-down {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-600);
    }

    .stats-trend-neutral {
        background: rgba(156, 163, 175, 0.1);
        color: var(--gray-600);
    }

    .stats-content {
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-900);
        line-height: 1;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 0.25rem;
    }

    .stats-description {
        font-size: 0.75rem;
        color: var(--gray-500);
    }

    .stats-progress {
        height: 4px;
        background: rgba(0, 0, 0, 0.05);
        border-radius: 2px;
        overflow: hidden;
    }

    .stats-progress .progress-bar {
        height: 100%;
        background: var(--primary-500);
        border-radius: 2px;
        transition: width 1s ease-in-out;
    }

    .stats-card-success .stats-progress .progress-bar { background: var(--success-500); }
    .stats-card-warning .stats-progress .progress-bar { background: var(--warning-500); }
    .stats-card-danger .stats-progress .progress-bar { background: var(--danger-500); }

    /* Dark theme support */
    [data-theme="dark"] .stats-card {
        background: var(--bg-card);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .stats-number {
        color: var(--text-primary);
    }

    [data-theme="dark"] .stats-label {
        color: var(--text-secondary);
    }

    [data-theme="dark"] .stats-description {
        color: var(--text-tertiary);
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .stats-card-body {
            padding: 1rem;
        }

        .stats-number {
            font-size: 1.5rem;
        }

        .stats-icon {
            width: 2.5rem;
            height: 2.5rem;
            font-size: 1rem;
        }
    }
    </style>

    <!-- Enhanced Status Toggle -->
    <div class="container-fluid mb-4">
        <div class="status-toggle-card">
            <div class="status-toggle-body">
                <div class="status-toggle-content">
                    <div class="toggle-section">
                        <!-- Modern Toggle Switch -->
                        <div class="modern-switch-container">
                            <input id="employeeStatusToggle" type="checkbox" class="modern-switch-input" {% if status != 'inactive' %}checked{% endif %}>
                            <label for="employeeStatusToggle" class="modern-switch-label">
                                <span class="modern-switch-slider">
                                    <span class="switch-icon-container">
                                        <i class="fas fa-user-check switch-icon-active"></i>
                                        <i class="fas fa-user-times switch-icon-inactive"></i>
                                    </span>
                                </span>
                            </label>
                        </div>

                        <div class="toggle-info">
                            <h5 id="toggleStatusText" class="toggle-title {% if status == 'inactive' %}text-danger{% else %}text-success{% endif %}">
                                <i id="toggleStatusIcon" class="fas {% if status == 'inactive' %}fa-user-times{% else %}fa-user-check{% endif %} me-2"></i>
                                <span id="toggleStatusLabel">{% if status == 'inactive' %}موظفين غير نشطين{% else %}موظفين نشطين{% endif %}</span>
                            </h5>
                            <p class="toggle-description">انقر للتبديل بين عرض الموظفين النشطين وغير النشطين</p>
                        </div>
                    </div>

                    <!-- Enhanced Mini Statistics -->
                    <div class="mini-stats-container">
                        <div class="mini-stat-item">
                            <div class="mini-stat-icon mini-stat-icon-success">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="mini-stat-content">
                                <div class="mini-stat-number text-success" id="toggleActiveCount">{{ active_employees }}</div>
                                <div class="mini-stat-label">نشط</div>
                            </div>
                        </div>
                        <div class="mini-stat-divider"></div>
                        <div class="mini-stat-item">
                            <div class="mini-stat-icon mini-stat-icon-danger">
                                <i class="fas fa-user-times"></i>
                            </div>
                            <div class="mini-stat-content">
                                <div class="mini-stat-number text-danger" id="toggleInactiveCount">{{ resigned_employees }}</div>
                                <div class="mini-stat-label">مستقيل</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    /* Enhanced Status Toggle Styles */
    .status-toggle-card {
        background: white;
        border-radius: var(--card-border-radius);
        box-shadow: var(--card-shadow);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: var(--transition-all);
    }

    .status-toggle-body {
        padding: 1.5rem;
    }

    .status-toggle-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
    }

    .toggle-section {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: 1;
    }

    /* Modern Switch Styles */
    .modern-switch-container {
        position: relative;
    }

    .modern-switch-input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .modern-switch-label {
        display: inline-block;
        width: 60px;
        height: 32px;
        background: #e5e7eb;
        border-radius: 16px;
        position: relative;
        cursor: pointer;
        transition: var(--transition-all);
        border: 2px solid transparent;
    }

    .modern-switch-input:checked + .modern-switch-label {
        background: var(--success-500);
    }

    .modern-switch-input:focus + .modern-switch-label {
        border-color: var(--primary-300);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .modern-switch-slider {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 26px;
        height: 26px;
        background: white;
        border-radius: 50%;
        transition: var(--transition-all);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .modern-switch-input:checked + .modern-switch-label .modern-switch-slider {
        transform: translateX(28px);
    }

    .switch-icon-container {
        font-size: 0.75rem;
        color: #6b7280;
    }

    .modern-switch-input:checked + .modern-switch-label .switch-icon-active {
        display: inline;
        color: var(--success-600);
    }

    .modern-switch-input:checked + .modern-switch-label .switch-icon-inactive {
        display: none;
    }

    .modern-switch-input:not(:checked) + .modern-switch-label .switch-icon-active {
        display: none;
    }

    .modern-switch-input:not(:checked) + .modern-switch-label .switch-icon-inactive {
        display: inline;
        color: #6b7280;
    }

    .toggle-info {
        flex: 1;
    }

    .toggle-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        display: flex;
        align-items: center;
    }

    .toggle-description {
        font-size: 0.875rem;
        color: var(--gray-600);
        margin: 0;
    }

    /* Mini Statistics */
    .mini-stats-container {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .mini-stat-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .mini-stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .mini-stat-icon-success {
        background: rgba(34, 197, 94, 0.1);
        color: var(--success-600);
    }

    .mini-stat-icon-danger {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-600);
    }

    .mini-stat-content {
        text-align: center;
    }

    .mini-stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .mini-stat-label {
        font-size: 0.75rem;
        color: var(--gray-600);
        font-weight: 500;
    }

    .mini-stat-divider {
        width: 1px;
        height: 2rem;
        background: var(--gray-200);
    }

    /* Dark theme support */
    [data-theme="dark"] .status-toggle-card {
        background: var(--bg-card);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .modern-switch-label {
        background: var(--gray-700);
    }

    [data-theme="dark"] .toggle-description {
        color: var(--text-tertiary);
    }

    [data-theme="dark"] .mini-stat-label {
        color: var(--text-tertiary);
    }

    [data-theme="dark"] .mini-stat-divider {
        background: var(--border-light);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .status-toggle-content {
            flex-direction: column;
            gap: 1.5rem;
            text-align: center;
        }

        .toggle-section {
            flex-direction: column;
            gap: 1rem;
        }

        .mini-stats-container {
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .status-toggle-body {
            padding: 1rem;
        }

        .mini-stats-container {
            gap: 1rem;
        }

        .mini-stat-icon {
            width: 2rem;
            height: 2rem;
            font-size: 0.875rem;
        }

        .mini-stat-number {
            font-size: 1.25rem;
        }
    }
    </style>


    <!-- Enhanced Search and Filter Section -->
    <div class="container-fluid mb-4">
        <div class="search-filter-card">
            <div class="search-filter-header">
                <div class="search-filter-title-section">
                    <div class="search-filter-title">
                        <i class="fas fa-search"></i>
                        <span>البحث والتصفية المتقدمة</span>
                    </div>
                    <div class="search-filter-subtitle">ابحث عن الموظفين باستخدام معايير متعددة ومتقدمة</div>
                </div>
                <div class="search-filter-actions">
                    <div class="results-badge">
                        <i class="fas fa-users"></i>
                        <span>{{ employees|length }} نتيجة</span>
                    </div>
                </div>
            </div>

            <div class="search-filter-body">
                <form method="get" action="" id="employeeFilterForm" class="modern-filter-form">
                    <!-- Active Filters -->
                    {% if request.GET %}
                    <div class="active-filters-section">
                        <div class="active-filters-header">
                            <div class="active-filters-title">
                                <i class="fas fa-filter"></i>
                                <span>الفلاتر النشطة</span>
                            </div>
                            <a href="{% url 'Hr:employees:list' %}" class="clear-filters-btn">
                                <i class="fas fa-times-circle"></i>
                                <span>مسح الكل</span>
                            </a>
                        </div>
                        <div class="filter-tags-container">
                            {% if request.GET.search %}
                            <div class="filter-tag filter-tag-primary">
                                <span class="filter-tag-label">البحث:</span>
                                <span class="filter-tag-value">{{ request.GET.search }}</span>
                                <button type="button" class="filter-tag-remove" data-field="search">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% endif %}
                            {% if request.GET.emp_code %}
                            <div class="filter-tag filter-tag-info">
                                <span class="filter-tag-label">الكود:</span>
                                <span class="filter-tag-value">{{ request.GET.emp_code }}</span>
                                <button type="button" class="filter-tag-remove" data-field="emp_code">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% endif %}
                            {% if request.GET.department %}
                            <div class="filter-tag filter-tag-success">
                                <span class="filter-tag-label">القسم:</span>
                                <span class="filter-tag-value">{{ filter_form.department.field.choices|dictsort:"0"|dict_get:request.GET.department }}</span>
                                <button type="button" class="filter-tag-remove" data-field="department">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% endif %}
                            {% if request.GET.job %}
                            <div class="filter-tag filter-tag-warning">
                                <span class="filter-tag-label">الوظيفة</span>
                                <button type="button" class="filter-tag-remove" data-field="job">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% endif %}
                            {% if request.GET.working_condition and request.GET.working_condition != 'سارى' %}
                            <div class="filter-tag filter-tag-danger">
                                <span class="filter-tag-label">حالة العمل:</span>
                                <span class="filter-tag-value">{{ request.GET.working_condition }}</span>
                                <button type="button" class="filter-tag-remove" data-field="working_condition">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% endif %}
                            {% if request.GET.insurance_status %}
                            <div class="filter-tag filter-tag-secondary">
                                <span class="filter-tag-label">حالة التأمين:</span>
                                <span class="filter-tag-value">{{ request.GET.insurance_status }}</span>
                                <button type="button" class="filter-tag-remove" data-field="insurance_status">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Enhanced Search Section -->
                    <div class="search-section">
                        <div class="search-input-container">
                            <div class="search-input-group">
                                <div class="search-input-wrapper">
                                    <div class="search-icon">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <input type="text"
                                           name="search"
                                           class="search-input"
                                           placeholder="بحث شامل: الاسم، الكود، الرقم القومي، الهاتف، العنوان..."
                                           value="{{ request.GET.search|default:'' }}"
                                           autocomplete="off"
                                           id="globalSearchInput">
                                    {% if request.GET.search %}
                                    <button type="button" class="search-clear" id="clearSearchBtn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                </div>
                                <button type="submit" class="search-submit-btn">
                                    <i class="fas fa-search"></i>
                                    <span>بحث</span>
                                </button>
                            </div>
                        </div>

                        <div class="search-actions">
                            <button type="button"
                                    class="action-btn action-btn-secondary"
                                    id="advancedSearchToggle"
                                    data-bs-toggle="collapse"
                                    data-bs-target="#advancedFilters"
                                    aria-expanded="false">
                                <i class="fas fa-sliders-h"></i>
                                <span>فلاتر متقدمة</span>
                            </button>
                            <button type="button"
                                    class="action-btn action-btn-secondary"
                                    id="viewToggle"
                                    title="تبديل العرض">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button type="button"
                                    class="action-btn action-btn-outline"
                                    id="exportBtn"
                                    title="تصدير البيانات">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>

    <style>
    /* Enhanced Search and Filter Styles */
    .search-filter-card {
        background: white;
        border-radius: var(--card-border-radius);
        box-shadow: var(--card-shadow);
        border: 1px solid rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .search-filter-header {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .search-filter-title-section {
        flex: 1;
        min-width: 250px;
    }

    .search-filter-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
    }

    .search-filter-subtitle {
        font-size: 0.875rem;
        color: var(--gray-600);
    }

    .search-filter-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .results-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: var(--primary-50);
        color: var(--primary-700);
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        border: 1px solid var(--primary-200);
    }

    .search-filter-body {
        padding: 1.5rem;
    }

    /* Active Filters */
    .active-filters-section {
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: #fef3c7;
        border: 1px solid #fbbf24;
        border-radius: 8px;
    }

    .active-filters-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .active-filters-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        color: #92400e;
    }

    .clear-filters-btn {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.75rem;
        background: white;
        color: #92400e;
        border: 1px solid #fbbf24;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
        text-decoration: none;
        transition: var(--transition-all);
    }

    .clear-filters-btn:hover {
        background: #fef3c7;
        color: #92400e;
        text-decoration: none;
    }

    .filter-tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .filter-tag {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.375rem 0.75rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
        border: 1px solid;
        position: relative;
    }

    .filter-tag-primary {
        background: var(--primary-50);
        color: var(--primary-700);
        border-color: var(--primary-200);
    }

    .filter-tag-info {
        background: var(--info-50);
        color: var(--info-700);
        border-color: var(--info-200);
    }

    .filter-tag-success {
        background: var(--success-50);
        color: var(--success-700);
        border-color: var(--success-200);
    }

    .filter-tag-warning {
        background: var(--warning-50);
        color: var(--warning-700);
        border-color: var(--warning-200);
    }

    .filter-tag-danger {
        background: var(--danger-50);
        color: var(--danger-700);
        border-color: var(--danger-200);
    }

    .filter-tag-secondary {
        background: var(--gray-50);
        color: var(--gray-700);
        border-color: var(--gray-200);
    }

    .filter-tag-label {
        font-weight: 600;
    }

    .filter-tag-value {
        font-weight: 400;
    }

    .filter-tag-remove {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 0.125rem;
        margin-left: 0.25rem;
        border-radius: 3px;
        transition: var(--transition-all);
        opacity: 0.7;
    }

    .filter-tag-remove:hover {
        opacity: 1;
        background: rgba(0, 0, 0, 0.1);
    }

    /* Search Section */
    .search-section {
        display: flex;
        gap: 1rem;
        align-items: flex-start;
        flex-wrap: wrap;
    }

    .search-input-container {
        flex: 1;
        min-width: 300px;
    }

    .search-input-group {
        display: flex;
        gap: 0.5rem;
    }

    .search-input-wrapper {
        flex: 1;
        position: relative;
        display: flex;
        align-items: center;
        background: white;
        border: 2px solid var(--gray-200);
        border-radius: 8px;
        transition: var(--transition-all);
    }

    .search-input-wrapper:focus-within {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .search-icon {
        padding: 0 0.75rem;
        color: var(--gray-400);
        font-size: 0.875rem;
    }

    .search-input {
        flex: 1;
        padding: 0.75rem 0.5rem;
        border: none;
        outline: none;
        font-size: 0.875rem;
        background: transparent;
        color: var(--gray-900);
    }

    .search-input::placeholder {
        color: var(--gray-400);
    }

    .search-clear {
        padding: 0.5rem;
        background: none;
        border: none;
        color: var(--gray-400);
        cursor: pointer;
        border-radius: 4px;
        transition: var(--transition-all);
    }

    .search-clear:hover {
        color: var(--gray-600);
        background: var(--gray-100);
    }

    .search-submit-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: var(--primary-600);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-all);
        white-space: nowrap;
    }

    .search-submit-btn:hover {
        background: var(--primary-700);
        transform: translateY(-1px);
    }

    .search-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .action-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        border: 1px solid;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition-all);
        background: white;
        text-decoration: none;
        white-space: nowrap;
    }

    .action-btn-secondary {
        border-color: var(--gray-300);
        color: var(--gray-700);
    }

    .action-btn-secondary:hover {
        background: var(--gray-50);
        border-color: var(--gray-400);
        color: var(--gray-800);
        text-decoration: none;
    }

    .action-btn-outline {
        border-color: var(--primary-300);
        color: var(--primary-600);
    }

    .action-btn-outline:hover {
        background: var(--primary-50);
        border-color: var(--primary-400);
        color: var(--primary-700);
        text-decoration: none;
    }

    /* Dark theme support */
    [data-theme="dark"] .search-filter-card {
        background: var(--bg-card);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .search-filter-header {
        background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .search-filter-title {
        color: var(--text-primary);
    }

    [data-theme="dark"] .search-filter-subtitle {
        color: var(--text-secondary);
    }

    [data-theme="dark"] .results-badge {
        background: var(--primary-900);
        color: var(--primary-100);
        border-color: var(--primary-700);
    }

    [data-theme="dark"] .search-input-wrapper {
        background: var(--bg-secondary);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .search-input {
        color: var(--text-primary);
    }

    [data-theme="dark"] .search-input::placeholder {
        color: var(--text-tertiary);
    }

    [data-theme="dark"] .action-btn {
        background: var(--bg-secondary);
        border-color: var(--border-light);
        color: var(--text-secondary);
    }

    [data-theme="dark"] .action-btn-secondary:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .search-filter-header {
            flex-direction: column;
            text-align: center;
        }

        .search-section {
            flex-direction: column;
        }

        .search-input-container {
            min-width: auto;
        }

        .search-actions {
            width: 100%;
            justify-content: center;
        }

        .action-btn {
            flex: 1;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .search-filter-body {
            padding: 1rem;
        }

        .search-input-group {
            flex-direction: column;
        }

        .search-submit-btn {
            width: 100%;
            justify-content: center;
        }

        .filter-tags-container {
            gap: 0.25rem;
        }

        .filter-tag {
            font-size: 0.6875rem;
            padding: 0.25rem 0.5rem;
        }
    }
    </style>

                    <!-- Enhanced Filter Groups -->
                    <div class="filter-groups-container">
                        <!-- Basic Filters -->
                        <div class="filter-group-card">
                            <div class="filter-group-header">
                                <h6 class="filter-group-title">
                                    <i class="fas fa-filter"></i>
                                    <span>الفلاتر الأساسية</span>
                                </h6>
                            </div>
                            <div class="filter-group-body">
                                <div class="row g-3">
                                    <div class="col-12 col-md-6 col-lg-3">
                                        <div class="modern-form-group">
                                            <label class="modern-form-label">
                                                <i class="fas fa-id-badge"></i>
                                                <span>كود الموظف</span>
                                            </label>
                                            <div class="modern-input-group">
                                                <div class="input-icon">
                                                    <i class="fas fa-hashtag"></i>
                                                </div>
                                                <input type="text"
                                                       name="emp_code"
                                                       class="modern-form-control"
                                                       placeholder="أدخل كود الموظف"
                                                       value="{{ request.GET.emp_code|default:'' }}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-6 col-lg-3">
                                        <div class="modern-form-group">
                                            <label class="modern-form-label">
                                                <i class="fas fa-building"></i>
                                                <span>{{ filter_form.department.label }}</span>
                                            </label>
                                            <div class="modern-select-wrapper">
                                                {{ filter_form.department }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-6 col-lg-3">
                                        <div class="modern-form-group">
                                            <label class="modern-form-label">
                                                <i class="fas fa-briefcase"></i>
                                                <span>{{ filter_form.job.label }}</span>
                                            </label>
                                            <div class="modern-select-wrapper">
                                                {{ filter_form.job }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-6 col-lg-3">
                                        <div class="modern-form-group">
                                            <label class="modern-form-label">
                                                <i class="fas fa-user-check"></i>
                                                <span>{{ filter_form.working_condition.label }}</span>
                                            </label>
                                            <div class="modern-select-wrapper">
                                                {{ filter_form.working_condition }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Filters (Collapsible) -->
                        <div class="collapse" id="advancedFilters">
                            <div class="filter-group-card advanced-filters-card">
                                <div class="filter-group-header">
                                    <h6 class="filter-group-title">
                                        <i class="fas fa-search-plus"></i>
                                        <span>فلاتر متقدمة</span>
                                    </h6>
                                </div>
                                <div class="filter-group-body">
                                    <div class="row g-3">
                                        <div class="col-12 col-md-6 col-lg-4">
                                            <div class="modern-form-group">
                                                <label class="modern-form-label">
                                                    <i class="fas fa-phone"></i>
                                                    <span>رقم الهاتف</span>
                                                </label>
                                                <div class="modern-input-group">
                                                    <div class="input-icon">
                                                        <i class="fas fa-phone"></i>
                                                    </div>
                                                    <input type="text"
                                                           name="phone"
                                                           class="modern-form-control"
                                                           placeholder="بحث برقم الهاتف"
                                                           value="{{ request.GET.phone|default:'' }}">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6 col-lg-4">
                                            <div class="modern-form-group">
                                                <label class="modern-form-label">
                                                    <i class="fas fa-id-card"></i>
                                                    <span>الرقم القومي</span>
                                                </label>
                                                <div class="modern-input-group">
                                                    <div class="input-icon">
                                                        <i class="fas fa-id-card"></i>
                                                    </div>
                                                    <input type="text"
                                                           name="national_id"
                                                           class="modern-form-control"
                                                           placeholder="بحث بالرقم القومي"
                                                           value="{{ request.GET.national_id|default:'' }}">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6 col-lg-4">
                                            <div class="modern-form-group">
                                                <label class="modern-form-label">
                                                    <i class="fas fa-shield-alt"></i>
                                                    <span>{{ filter_form.insurance_status.label }}</span>
                                                </label>
                                                <div class="modern-select-wrapper">
                                                    {{ filter_form.insurance_status }}
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6 col-lg-4">
                                            <div class="modern-form-group">
                                                <label class="modern-form-label">
                                                    <i class="fas fa-car"></i>
                                                    <span>السيارة</span>
                                                </label>
                                                <div class="modern-input-group">
                                                    <div class="input-icon">
                                                        <i class="fas fa-car"></i>
                                                    </div>
                                                    <input type="text"
                                                           name="car"
                                                           class="modern-form-control"
                                                           placeholder="بحث بالسيارة"
                                                           value="{{ request.GET.car|default:'' }}">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6 col-lg-4">
                                            <div class="modern-form-group">
                                                <label class="modern-form-label">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <span>نقطة التقاط السيارة</span>
                                                </label>
                                                <div class="modern-input-group">
                                                    <div class="input-icon">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </div>
                                                    <input type="text"
                                                           name="car_pick_up_point"
                                                           class="modern-form-control"
                                                           placeholder="نقطة التقاط السيارة"
                                                           value="{{ request.GET.car_pick_up_point|default:'' }}">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-md-6 col-lg-4">
                                            <div class="modern-form-group">
                                                <label class="modern-form-label">
                                                    <i class="fas fa-clock"></i>
                                                    <span>نوع الوردية</span>
                                                </label>
                                                <div class="modern-select-wrapper">
                                                    <select name="shift_type" class="modern-form-select">
                                                        <option value="">جميع الورديات</option>
                                                        <option value="صباحي" {% if request.GET.shift_type == 'صباحي' %}selected{% endif %}>صباحي</option>
                                                        <option value="مسائي" {% if request.GET.shift_type == 'مسائي' %}selected{% endif %}>مسائي</option>
                                                        <option value="ليلي" {% if request.GET.shift_type == 'ليلي' %}selected{% endif %}>ليلي</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

    <style>
    /* Enhanced Filter Form Styles */
    .filter-groups-container {
        margin-top: 1.5rem;
    }

    .filter-group-card {
        background: white;
        border: 1px solid var(--gray-200);
        border-radius: 8px;
        margin-bottom: 1rem;
        overflow: hidden;
        transition: var(--transition-all);
    }

    .filter-group-card:hover {
        border-color: var(--gray-300);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .advanced-filters-card {
        border-color: var(--primary-200);
        background: var(--primary-25);
    }

    .filter-group-header {
        background: var(--gray-50);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .advanced-filters-card .filter-group-header {
        background: var(--primary-50);
        border-color: var(--primary-200);
    }

    .filter-group-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--gray-700);
        margin: 0;
    }

    .advanced-filters-card .filter-group-title {
        color: var(--primary-700);
    }

    .filter-group-body {
        padding: 1.5rem;
    }

    /* Modern Form Controls */
    .modern-form-group {
        margin-bottom: 0;
    }

    .modern-form-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
    }

    .modern-input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-icon {
        position: absolute;
        left: 0.75rem;
        z-index: 2;
        color: var(--gray-400);
        font-size: 0.875rem;
    }

    .modern-form-control {
        width: 100%;
        padding: 0.75rem 0.75rem 0.75rem 2.5rem;
        border: 2px solid var(--gray-200);
        border-radius: 8px;
        font-size: 0.875rem;
        background: white;
        color: var(--gray-900);
        transition: var(--transition-all);
    }

    .modern-form-control:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .modern-form-control::placeholder {
        color: var(--gray-400);
    }

    .modern-select-wrapper {
        position: relative;
    }

    .modern-select-wrapper::after {
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-400);
        pointer-events: none;
        z-index: 2;
    }

    .modern-form-select,
    .modern-select-wrapper select {
        width: 100%;
        padding: 0.75rem 2.5rem 0.75rem 0.75rem;
        border: 2px solid var(--gray-200);
        border-radius: 8px;
        font-size: 0.875rem;
        background: white;
        color: var(--gray-900);
        transition: var(--transition-all);
        appearance: none;
        cursor: pointer;
    }

    .modern-form-select:focus,
    .modern-select-wrapper select:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Dark theme support */
    [data-theme="dark"] .filter-group-card {
        background: var(--bg-card);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .filter-group-header {
        background: var(--bg-secondary);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .filter-group-title {
        color: var(--text-primary);
    }

    [data-theme="dark"] .modern-form-label {
        color: var(--text-secondary);
    }

    [data-theme="dark"] .modern-form-control,
    [data-theme="dark"] .modern-form-select,
    [data-theme="dark"] .modern-select-wrapper select {
        background: var(--bg-secondary);
        border-color: var(--border-light);
        color: var(--text-primary);
    }

    [data-theme="dark"] .modern-form-control::placeholder {
        color: var(--text-tertiary);
    }

    [data-theme="dark"] .input-icon {
        color: var(--text-tertiary);
    }

    [data-theme="dark"] .modern-select-wrapper::after {
        color: var(--text-tertiary);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .filter-group-body {
            padding: 1rem;
        }

        .modern-form-control,
        .modern-form-select,
        .modern-select-wrapper select {
            padding: 0.625rem 0.625rem 0.625rem 2.25rem;
            font-size: 0.8125rem;
        }

        .input-icon {
            left: 0.625rem;
            font-size: 0.8125rem;
        }

        .modern-select-wrapper::after {
            right: 0.625rem;
        }
    }
    </style>

                    <!-- Enhanced Action Buttons -->
                    <div class="filter-actions-container">
                        <div class="filter-actions-row">
                            <div class="primary-actions">
                                <button type="submit" class="filter-action-btn filter-action-primary">
                                    <i class="fas fa-search"></i>
                                    <span>تطبيق الفلاتر</span>
                                </button>
                                <a href="{% url 'Hr:employees:list' %}" class="filter-action-btn filter-action-secondary">
                                    <i class="fas fa-undo"></i>
                                    <span>إعادة تعيين</span>
                                </a>
                            </div>
                            <div class="secondary-actions">
                                <button type="button"
                                        class="filter-action-btn filter-action-outline"
                                        data-bs-toggle="collapse"
                                        data-bs-target="#advancedFilters"
                                        aria-expanded="false"
                                        aria-controls="advancedFilters">
                                    <i class="fas fa-sliders-h"></i>
                                    <span>فلاتر متقدمة</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <style>
    /* Enhanced Filter Actions */
    .filter-actions-container {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--gray-200);
    }

    .filter-actions-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .primary-actions,
    .secondary-actions {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .filter-action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-all);
        text-decoration: none;
        border: 2px solid;
        white-space: nowrap;
    }

    .filter-action-primary {
        background: var(--primary-600);
        color: white;
        border-color: var(--primary-600);
    }

    .filter-action-primary:hover {
        background: var(--primary-700);
        border-color: var(--primary-700);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        color: white;
        text-decoration: none;
    }

    .filter-action-secondary {
        background: var(--gray-100);
        color: var(--gray-700);
        border-color: var(--gray-300);
    }

    .filter-action-secondary:hover {
        background: var(--gray-200);
        border-color: var(--gray-400);
        color: var(--gray-800);
        text-decoration: none;
    }

    .filter-action-outline {
        background: transparent;
        color: var(--primary-600);
        border-color: var(--primary-300);
    }

    .filter-action-outline:hover {
        background: var(--primary-50);
        border-color: var(--primary-400);
        color: var(--primary-700);
        text-decoration: none;
    }

    /* Dark theme support */
    [data-theme="dark"] .filter-actions-container {
        border-color: var(--border-light);
    }

    [data-theme="dark"] .filter-action-secondary {
        background: var(--bg-secondary);
        color: var(--text-secondary);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .filter-action-secondary:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    [data-theme="dark"] .filter-action-outline {
        color: var(--primary-400);
        border-color: var(--primary-600);
    }

    [data-theme="dark"] .filter-action-outline:hover {
        background: var(--primary-900);
        color: var(--primary-300);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .filter-actions-row {
            flex-direction: column;
            align-items: stretch;
        }

        .primary-actions,
        .secondary-actions {
            width: 100%;
            justify-content: center;
        }

        .filter-action-btn {
            flex: 1;
            justify-content: center;
            min-width: 120px;
        }
    }

    @media (max-width: 576px) {
        .primary-actions,
        .secondary-actions {
            flex-direction: column;
        }

        .filter-action-btn {
            width: 100%;
        }
    }
    </style>

    <!-- Enhanced Employee List Container -->
    <div class="container-fluid mb-4">
        <div class="employee-list-card">
            <div class="employee-list-header">
                <div class="employee-list-title-section">
                    <div class="employee-list-title" id="employeeListTitle">
                        {% if status == 'inactive' %}
                        <div class="title-icon title-icon-danger">
                            <i id="employeeListIcon" class="fas fa-user-times"></i>
                        </div>
                        <div class="title-content">
                            <h5 id="employeeListTitleText">قائمة الموظفين غير النشطين</h5>
                            <p class="title-description">الموظفين المستقيلين أو المفصولين</p>
                        </div>
                        {% else %}
                        <div class="title-icon title-icon-success">
                            <i id="employeeListIcon" class="fas fa-users"></i>
                        </div>
                        <div class="title-content">
                            <h5 id="employeeListTitleText">قائمة الموظفين النشطين</h5>
                            <p class="title-description">الموظفين العاملين حالياً في النظام</p>
                        </div>
                        {% endif %}
                    </div>
                    {% if employees %}
                    <div class="employee-list-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="employeeListCount">{{ employees|length }}</span>
                            <span class="stat-label">موظف</span>
                        </div>
                        {% if request.GET %}
                        <div class="stat-divider"></div>
                        <div class="stat-item">
                            <span class="stat-text">
                                <i class="fas fa-filter"></i>
                                مطابق لمعايير البحث
                            </span>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                <div class="employee-list-actions">
                    <div class="view-controls">
                        <div class="view-toggle-group" role="group">
                            <button type="button"
                                    class="view-toggle-btn view-toggle-active"
                                    id="tableViewBtn"
                                    title="عرض جدول">
                                <i class="fas fa-table"></i>
                                <span class="view-label">جدول</span>
                            </button>
                            <button type="button"
                                    class="view-toggle-btn"
                                    id="cardViewBtn"
                                    title="عرض بطاقات">
                                <i class="fas fa-th-large"></i>
                                <span class="view-label">بطاقات</span>
                            </button>
                        </div>
                    </div>

                    <div class="list-actions">
                        <!-- Sort Dropdown -->
                        <div class="sort-dropdown">
                            <button class="list-action-btn dropdown-toggle"
                                    type="button"
                                    id="sortDropdown"
                                    data-bs-toggle="dropdown"
                                    aria-expanded="false"
                                    title="ترتيب القائمة">
                                <i class="fas fa-sort"></i>
                                <span class="action-label">ترتيب</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end modern-dropdown" aria-labelledby="sortDropdown">
                                <li><h6 class="dropdown-header">ترتيب حسب</h6></li>
                                <li><a class="dropdown-item sort-option" href="#" data-sort="emp_id">
                                    <i class="fas fa-hashtag"></i>رقم الموظف
                                </a></li>
                                <li><a class="dropdown-item sort-option" href="#" data-sort="emp_full_name">
                                    <i class="fas fa-user"></i>الاسم
                                </a></li>
                                <li><a class="dropdown-item sort-option" href="#" data-sort="department">
                                    <i class="fas fa-building"></i>القسم
                                </a></li>
                                <li><a class="dropdown-item sort-option" href="#" data-sort="emp_date_hiring">
                                    <i class="fas fa-calendar"></i>تاريخ التعيين
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header">اتجاه الترتيب</h6></li>
                                <li><a class="dropdown-item sort-direction" href="#" data-direction="asc">
                                    <i class="fas fa-arrow-up"></i>تصاعدي
                                </a></li>
                                <li><a class="dropdown-item sort-direction" href="#" data-direction="desc">
                                    <i class="fas fa-arrow-down"></i>تنازلي
                                </a></li>
                            </ul>
                        </div>

                        <!-- Additional Actions -->
                        <button type="button"
                                class="list-action-btn"
                                id="refreshListBtn"
                                title="تحديث القائمة">
                            <i class="fas fa-sync-alt"></i>
                        </button>

                        <button type="button"
                                class="list-action-btn"
                                id="printListBtn"
                                title="طباعة القائمة">
                            <i class="fas fa-print"></i>
                        </button>

                        <!-- Add Employee Button -->
                        {% if perms.Hr.add_employee or user|is_admin %}
                        <a href="{% url 'Hr:employees:create' %}" class="primary-action-btn">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة موظف جديد</span>
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>

    <style>
    /* Enhanced Employee List Header Styles */
    .employee-list-card {
        background: white;
        border: 1px solid var(--gray-200);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: var(--transition-all);
    }

    .employee-list-header {
        background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--primary-200);
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .employee-list-title-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        flex: 1;
        min-width: 300px;
    }

    .employee-list-title {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .title-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .title-icon-success {
        background: var(--success-100);
        color: var(--success-600);
    }

    .title-icon-danger {
        background: var(--danger-100);
        color: var(--danger-600);
    }

    .title-content h5 {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-900);
        margin: 0;
        line-height: 1.2;
    }

    .title-description {
        font-size: 0.875rem;
        color: var(--gray-600);
        margin: 0.25rem 0 0 0;
        line-height: 1.4;
    }

    .employee-list-stats {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-600);
        line-height: 1;
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--gray-600);
        font-weight: 500;
    }

    .stat-text {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: var(--gray-600);
    }

    .stat-divider {
        width: 1px;
        height: 24px;
        background: var(--gray-300);
    }

    .employee-list-actions {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        flex-wrap: wrap;
    }

    .view-controls {
        display: flex;
        align-items: center;
    }

    .view-toggle-group {
        display: flex;
        background: white;
        border: 1px solid var(--gray-300);
        border-radius: 8px;
        overflow: hidden;
    }

    .view-toggle-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: transparent;
        border: none;
        color: var(--gray-600);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition-all);
        border-right: 1px solid var(--gray-300);
    }

    .view-toggle-btn:last-child {
        border-right: none;
    }

    .view-toggle-btn:hover {
        background: var(--gray-50);
        color: var(--gray-800);
    }

    .view-toggle-active {
        background: var(--primary-600) !important;
        color: white !important;
    }

    .view-label {
        font-size: 0.8125rem;
    }

    .list-actions {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .list-action-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem;
        background: white;
        border: 1px solid var(--gray-300);
        border-radius: 8px;
        color: var(--gray-600);
        font-size: 0.875rem;
        cursor: pointer;
        transition: var(--transition-all);
        text-decoration: none;
    }

    .list-action-btn:hover {
        background: var(--gray-50);
        border-color: var(--gray-400);
        color: var(--gray-800);
        text-decoration: none;
    }

    .action-label {
        font-size: 0.8125rem;
        font-weight: 500;
    }

    .primary-action-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: var(--primary-600);
        color: white;
        border: 1px solid var(--primary-600);
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-all);
        text-decoration: none;
    }

    .primary-action-btn:hover {
        background: var(--primary-700);
        border-color: var(--primary-700);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        color: white;
        text-decoration: none;
    }

    /* Modern Dropdown Styles */
    .modern-dropdown {
        border: 1px solid var(--gray-200);
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 0.5rem 0;
        min-width: 200px;
    }

    .modern-dropdown .dropdown-header {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--gray-500);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 0.5rem 1rem;
        margin-bottom: 0.25rem;
    }

    .modern-dropdown .dropdown-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        color: var(--gray-700);
        transition: var(--transition-all);
    }

    .modern-dropdown .dropdown-item:hover {
        background: var(--primary-50);
        color: var(--primary-700);
    }

    .modern-dropdown .dropdown-item i {
        width: 16px;
        text-align: center;
        color: var(--gray-400);
    }

    .modern-dropdown .dropdown-item:hover i {
        color: var(--primary-600);
    }

    /* Dark theme support */
    [data-theme="dark"] .employee-list-card {
        background: var(--bg-card);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .employee-list-header {
        background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .title-content h5 {
        color: var(--text-primary);
    }

    [data-theme="dark"] .title-description {
        color: var(--text-secondary);
    }

    [data-theme="dark"] .stat-label,
    [data-theme="dark"] .stat-text {
        color: var(--text-secondary);
    }

    [data-theme="dark"] .view-toggle-group {
        background: var(--bg-secondary);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .view-toggle-btn {
        color: var(--text-secondary);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .view-toggle-btn:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    [data-theme="dark"] .list-action-btn {
        background: var(--bg-secondary);
        border-color: var(--border-light);
        color: var(--text-secondary);
    }

    [data-theme="dark"] .list-action-btn:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    [data-theme="dark"] .modern-dropdown {
        background: var(--bg-card);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .modern-dropdown .dropdown-item {
        color: var(--text-secondary);
    }

    [data-theme="dark"] .modern-dropdown .dropdown-item:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    /* Responsive adjustments */
    @media (max-width: 1024px) {
        .employee-list-header {
            padding: 1.25rem 1.5rem;
            gap: 1.5rem;
        }

        .employee-list-title-section {
            min-width: 250px;
        }
    }

    @media (max-width: 768px) {
        .employee-list-header {
            flex-direction: column;
            align-items: stretch;
            gap: 1.5rem;
            padding: 1rem;
        }

        .employee-list-title-section {
            min-width: auto;
        }

        .employee-list-actions {
            justify-content: space-between;
            gap: 1rem;
        }

        .view-toggle-btn {
            padding: 0.625rem 0.75rem;
        }

        .view-label {
            display: none;
        }

        .action-label {
            display: none;
        }

        .primary-action-btn {
            padding: 0.75rem 1rem;
        }

        .primary-action-btn span {
            display: none;
        }
    }

    @media (max-width: 576px) {
        .employee-list-stats {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .stat-divider {
            display: none;
        }

        .list-actions {
            flex-wrap: wrap;
            justify-content: center;
        }
    }

    /* Enhanced Employee List Body Styles */
    .employee-list-body {
        background: white;
        border-radius: 0 0 12px 12px;
        overflow: hidden;
    }

    /* Modern Table Container Styles */
    .modern-table-container {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .table-wrapper {
        overflow-x: auto;
        scrollbar-width: thin;
        scrollbar-color: var(--gray-300) var(--gray-100);
    }

    .table-wrapper::-webkit-scrollbar {
        height: 8px;
    }

    .table-wrapper::-webkit-scrollbar-track {
        background: var(--gray-100);
    }

    .table-wrapper::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: 4px;
    }

    .table-wrapper::-webkit-scrollbar-thumb:hover {
        background: var(--gray-400);
    }

    /* Modern Employee Table Styles */
    .modern-employee-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.875rem;
        background: white;
        margin: 0;
    }

    /* Table Header Styles */
    .modern-table-header {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
        border-bottom: 2px solid var(--gray-200);
    }

    .header-row {
        border: none;
    }

    .modern-table-th {
        padding: 1rem 1.5rem;
        text-align: right;
        font-weight: 600;
        color: var(--gray-700);
        border: none;
        border-bottom: 1px solid var(--gray-200);
        white-space: nowrap;
        position: relative;
        background: transparent;
    }

    .column-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.75rem;
        min-height: 24px;
    }

    .column-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
    }

    .column-icon {
        color: var(--gray-500);
        font-size: 0.875rem;
        width: 16px;
        text-align: center;
        flex-shrink: 0;
    }

    .column-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--gray-700);
        line-height: 1.2;
    }

    .sort-indicator {
        display: flex;
        align-items: center;
        opacity: 0.6;
        transition: var(--transition-all);
    }

    .sort-icon {
        font-size: 0.75rem;
        color: var(--gray-400);
        transition: var(--transition-all);
    }

    .sortable-column {
        cursor: pointer;
        transition: var(--transition-all);
    }

    .sortable-column:hover {
        background: var(--gray-100);
    }

    .sortable-column:hover .sort-indicator {
        opacity: 1;
    }

    .sortable-column:hover .sort-icon {
        color: var(--primary-600);
    }

    .sortable-column.sort-asc .sort-icon {
        color: var(--primary-600);
        transform: rotate(180deg);
    }

    .sortable-column.sort-desc .sort-icon {
        color: var(--primary-600);
    }

    .actions-column {
        width: 120px;
        min-width: 120px;
    }

    /* Table Body Styles */
    .modern-table-body {
        background: white;
    }

    .modern-table-row {
        border: none;
        transition: var(--transition-all);
        position: relative;
    }

    .modern-table-row:hover {
        background: var(--gray-50);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .modern-table-row:not(:last-child) {
        border-bottom: 1px solid var(--gray-100);
    }

    /* Table Cell Styles */
    .modern-table-cell {
        padding: 1.25rem 1.5rem;
        vertical-align: middle;
        border: none;
        position: relative;
    }

    .cell-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        min-height: 24px;
    }

    /* Employee ID Cell */
    .id-cell .cell-content {
        justify-content: flex-start;
    }

    .employee-id-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.375rem 0.75rem;
        background: var(--primary-100);
        color: var(--primary-700);
        border-radius: 6px;
        font-size: 0.8125rem;
        font-weight: 600;
        border: 1px solid var(--primary-200);
    }

    /* Employee Info Cell */
    .employee-info-cell {
        min-width: 250px;
    }

    .employee-profile {
        display: flex;
        align-items: center;
        gap: 1rem;
        width: 100%;
    }

    .employee-avatar-wrapper {
        flex-shrink: 0;
    }

    .employee-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid var(--gray-200);
        transition: var(--transition-all);
    }

    .employee-avatar-placeholder {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: var(--primary-100);
        color: var(--primary-700);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.125rem;
        font-weight: 700;
        border: 2px solid var(--primary-200);
    }

    .employee-details {
        flex: 1;
        min-width: 0;
    }

    .employee-name-wrapper {
        margin-bottom: 0.25rem;
    }

    .employee-name-link {
        font-size: 0.9375rem;
        font-weight: 600;
        color: var(--gray-900);
        text-decoration: none;
        transition: var(--transition-all);
        line-height: 1.3;
    }

    .employee-name-link:hover {
        color: var(--primary-600);
        text-decoration: none;
    }

    .employee-meta-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        font-size: 0.8125rem;
        color: var(--gray-500);
    }

    .meta-icon {
        font-size: 0.75rem;
        color: var(--gray-400);
    }

    .meta-text {
        line-height: 1.2;
    }

    /* Department Cell */
    .department-cell .cell-content {
        justify-content: flex-start;
    }

    .department-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.375rem 0.75rem;
        background: var(--info-100);
        color: var(--info-700);
        border-radius: 6px;
        font-size: 0.8125rem;
        font-weight: 500;
        border: 1px solid var(--info-200);
        max-width: 100%;
    }

    .badge-icon {
        font-size: 0.75rem;
        flex-shrink: 0;
    }

    .badge-text {
        line-height: 1.2;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Job Cell */
    .job-cell .cell-content {
        justify-content: flex-start;
    }

    .job-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--gray-700);
        line-height: 1.3;
    }

    /* Phone Cell */
    .phone-cell .cell-content {
        justify-content: flex-start;
    }

    .phone-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--primary-600);
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: var(--transition-all);
        padding: 0.25rem 0;
    }

    .phone-link:hover {
        color: var(--primary-700);
        text-decoration: none;
    }

    .phone-icon {
        font-size: 0.75rem;
        flex-shrink: 0;
    }

    .phone-number {
        line-height: 1.2;
    }

    /* Status Cell */
    .status-cell .cell-content {
        justify-content: flex-start;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.375rem 0.75rem;
        border-radius: 6px;
        font-size: 0.8125rem;
        font-weight: 500;
        border: 1px solid;
        white-space: nowrap;
    }

    .status-active {
        background: var(--success-100);
        color: var(--success-700);
        border-color: var(--success-200);
    }

    .status-inactive {
        background: var(--warning-100);
        color: var(--warning-700);
        border-color: var(--warning-200);
    }

    .status-resigned {
        background: var(--danger-100);
        color: var(--danger-700);
        border-color: var(--danger-200);
    }

    .status-unknown {
        background: var(--gray-100);
        color: var(--gray-700);
        border-color: var(--gray-200);
    }

    .status-icon {
        font-size: 0.75rem;
        flex-shrink: 0;
    }

    .status-text {
        line-height: 1.2;
    }

    /* Actions Cell */
    .actions-cell {
        width: 120px;
        min-width: 120px;
    }

    .actions-cell .cell-content {
        justify-content: center;
    }

    .action-buttons {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .primary-actions {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: 1px solid;
        background: white;
        color: var(--gray-600);
        text-decoration: none;
        font-size: 0.875rem;
        transition: var(--transition-all);
        cursor: pointer;
    }

    .view-btn {
        border-color: var(--primary-300);
        color: var(--primary-600);
    }

    .view-btn:hover {
        background: var(--primary-50);
        border-color: var(--primary-400);
        color: var(--primary-700);
        text-decoration: none;
        transform: translateY(-1px);
    }

    .edit-btn {
        border-color: var(--primary-600);
        background: var(--primary-600);
        color: white;
    }

    .edit-btn:hover {
        background: var(--primary-700);
        border-color: var(--primary-700);
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .more-btn {
        border-color: var(--gray-300);
        color: var(--gray-500);
    }

    .more-btn:hover {
        background: var(--gray-50);
        border-color: var(--gray-400);
        color: var(--gray-700);
    }

    /* Empty Value */
    .empty-value {
        color: var(--gray-400);
        font-style: italic;
        font-size: 0.875rem;
    }

    /* Dark theme support for table */
    [data-theme="dark"] .employee-list-body {
        background: var(--bg-card);
    }

    [data-theme="dark"] .modern-table-container {
        background: var(--bg-card);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .modern-employee-table {
        background: var(--bg-card);
    }

    [data-theme="dark"] .modern-table-header {
        background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .modern-table-th {
        color: var(--text-secondary);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .column-title {
        color: var(--text-secondary);
    }

    [data-theme="dark"] .column-icon {
        color: var(--text-tertiary);
    }

    [data-theme="dark"] .sortable-column:hover {
        background: var(--bg-tertiary);
    }

    [data-theme="dark"] .modern-table-body {
        background: var(--bg-card);
    }

    [data-theme="dark"] .modern-table-row:hover {
        background: var(--bg-secondary);
    }

    [data-theme="dark"] .modern-table-row:not(:last-child) {
        border-color: var(--border-light);
    }

    [data-theme="dark"] .employee-name-link {
        color: var(--text-primary);
    }

    [data-theme="dark"] .employee-name-link:hover {
        color: var(--primary-400);
    }

    [data-theme="dark"] .meta-item {
        color: var(--text-tertiary);
    }

    [data-theme="dark"] .meta-icon {
        color: var(--text-quaternary);
    }

    [data-theme="dark"] .job-title {
        color: var(--text-secondary);
    }

    [data-theme="dark"] .phone-link {
        color: var(--primary-400);
    }

    [data-theme="dark"] .phone-link:hover {
        color: var(--primary-300);
    }

    [data-theme="dark"] .action-btn {
        background: var(--bg-secondary);
        color: var(--text-secondary);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .view-btn {
        border-color: var(--primary-600);
        color: var(--primary-400);
    }

    [data-theme="dark"] .view-btn:hover {
        background: var(--bg-tertiary);
        color: var(--primary-300);
    }

    [data-theme="dark"] .more-btn:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    [data-theme="dark"] .empty-value {
        color: var(--text-quaternary);
    }

    /* Responsive table styles */
    @media (max-width: 1200px) {
        .modern-table-th,
        .modern-table-cell {
            padding: 1rem 1.25rem;
        }

        .employee-profile {
            gap: 0.75rem;
        }

        .employee-avatar,
        .employee-avatar-placeholder {
            width: 40px;
            height: 40px;
        }

        .employee-avatar-placeholder {
            font-size: 1rem;
        }
    }

    @media (max-width: 992px) {
        .modern-table-th,
        .modern-table-cell {
            padding: 0.875rem 1rem;
        }

        .column-content {
            gap: 0.375rem;
        }

        .column-title {
            font-size: 0.8125rem;
        }

        .employee-info-cell {
            min-width: 200px;
        }

        .badge-text {
            max-width: 100px;
        }
    }

    @media (max-width: 768px) {
        .modern-table-container {
            border-radius: 0;
            box-shadow: none;
            border: 1px solid var(--gray-200);
        }

        .table-wrapper {
            border-radius: 0;
        }

        .modern-table-th,
        .modern-table-cell {
            padding: 0.75rem 0.875rem;
        }

        .employee-info-cell {
            min-width: 180px;
        }

        .employee-avatar,
        .employee-avatar-placeholder {
            width: 36px;
            height: 36px;
        }

        .employee-avatar-placeholder {
            font-size: 0.875rem;
        }

        .employee-name-link {
            font-size: 0.875rem;
        }

        .meta-item {
            font-size: 0.75rem;
        }

        .action-btn {
            width: 28px;
            height: 28px;
            font-size: 0.8125rem;
        }

        .primary-actions {
            gap: 0.125rem;
        }
    }

    @media (max-width: 576px) {
        .modern-table-th,
        .modern-table-cell {
            padding: 0.625rem 0.75rem;
        }

        .column-content {
            gap: 0.25rem;
        }

        .column-title {
            font-size: 0.75rem;
        }

        .employee-info-cell {
            min-width: 160px;
        }

        .employee-profile {
            gap: 0.5rem;
        }

        .employee-avatar,
        .employee-avatar-placeholder {
            width: 32px;
            height: 32px;
        }

        .employee-avatar-placeholder {
            font-size: 0.8125rem;
        }

        .employee-name-link {
            font-size: 0.8125rem;
        }

        .badge-text {
            max-width: 80px;
        }

        .action-btn {
            width: 24px;
            height: 24px;
            font-size: 0.75rem;
        }
    }

    /* Modern Cards Container Styles */
    .modern-cards-container {
        padding: 0;
    }

    .employee-cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 1.5rem;
        padding: 0;
    }

    /* Modern Employee Card Styles */
    .modern-employee-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: var(--transition-all);
        overflow: hidden;
        border: 1px solid var(--gray-200);
        position: relative;
    }

    .modern-employee-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: var(--primary-300);
    }

    .card-container {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .card-content {
        padding: 1.5rem;
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
    }

    /* Card Header Section */
    .card-header-section {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 0.5rem;
    }

    .employee-avatar-section {
        position: relative;
        flex-shrink: 0;
    }

    .card-avatar {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid var(--gray-200);
        transition: var(--transition-all);
    }

    .card-avatar-placeholder {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background: var(--primary-100);
        color: var(--primary-700);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 700;
        border: 3px solid var(--primary-200);
    }

    .status-indicator-wrapper {
        position: absolute;
        bottom: 2px;
        right: 2px;
    }

    .status-dot {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .status-dot.status-active {
        background: var(--success-500);
    }

    .status-dot.status-inactive {
        background: var(--warning-500);
    }

    .status-dot.status-resigned {
        background: var(--danger-500);
    }

    .status-dot.status-unknown {
        background: var(--gray-400);
    }

    .employee-basic-info {
        flex: 1;
        min-width: 0;
    }

    .card-employee-name {
        margin: 0 0 0.5rem 0;
        font-size: 1.125rem;
        font-weight: 600;
        line-height: 1.3;
    }

    .name-link {
        color: var(--gray-900);
        text-decoration: none;
        transition: var(--transition-all);
    }

    .name-link:hover {
        color: var(--primary-600);
        text-decoration: none;
    }

    .card-employee-id {
        margin-top: 0.25rem;
    }

    .id-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.25rem 0.5rem;
        background: var(--primary-100);
        color: var(--primary-700);
        border-radius: 4px;
        font-size: 0.8125rem;
        font-weight: 600;
        border: 1px solid var(--primary-200);
    }

    /* Card Body Section */
    .card-body-section {
        flex: 1;
    }

    .employee-details-grid {
        display: flex;
        flex-direction: column;
        gap: 0.875rem;
    }

    .detail-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.5rem 0;
    }

    .detail-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--gray-500);
        font-size: 0.875rem;
        flex-shrink: 0;
        margin-top: 0.125rem;
    }

    .detail-content {
        flex: 1;
        min-width: 0;
    }

    .detail-label {
        display: block;
        font-size: 0.8125rem;
        font-weight: 500;
        color: var(--gray-600);
        margin-bottom: 0.25rem;
        line-height: 1.2;
    }

    .detail-value {
        display: block;
        font-size: 0.875rem;
        font-weight: 400;
        color: var(--gray-800);
        line-height: 1.3;
        word-break: break-word;
    }

    .detail-link {
        color: var(--primary-600);
        text-decoration: none;
        transition: var(--transition-all);
    }

    .detail-link:hover {
        color: var(--primary-700);
        text-decoration: none;
    }

    /* Card Status Section */
    .card-status-section {
        margin-top: auto;
        padding-top: 1rem;
        border-top: 1px solid var(--gray-100);
    }

    .card-status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        font-size: 0.8125rem;
        font-weight: 500;
        border: 1px solid;
        width: 100%;
        justify-content: center;
    }

    .card-status-badge.status-active {
        background: var(--success-100);
        color: var(--success-700);
        border-color: var(--success-200);
    }

    .card-status-badge.status-inactive {
        background: var(--warning-100);
        color: var(--warning-700);
        border-color: var(--warning-200);
    }

    .card-status-badge.status-resigned {
        background: var(--danger-100);
        color: var(--danger-700);
        border-color: var(--danger-200);
    }

    .card-status-badge.status-unknown {
        background: var(--gray-100);
        color: var(--gray-700);
        border-color: var(--gray-200);
    }

    /* Card Actions Section */
    .card-actions-section {
        padding: 1rem 1.5rem;
        background: var(--gray-50);
        border-top: 1px solid var(--gray-100);
        margin-top: auto;
    }

    .card-action-buttons {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .card-action-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        border: 1px solid;
        background: white;
        color: var(--gray-600);
        text-decoration: none;
        font-size: 0.8125rem;
        font-weight: 500;
        transition: var(--transition-all);
        cursor: pointer;
        flex: 1;
        justify-content: center;
        min-height: 36px;
    }

    .primary-action {
        border-color: var(--primary-300);
        color: var(--primary-600);
    }

    .primary-action:hover {
        background: var(--primary-50);
        border-color: var(--primary-400);
        color: var(--primary-700);
        text-decoration: none;
        transform: translateY(-1px);
    }

    .secondary-action {
        border-color: var(--primary-600);
        background: var(--primary-600);
        color: white;
    }

    .secondary-action:hover {
        background: var(--primary-700);
        border-color: var(--primary-700);
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .more-action {
        border-color: var(--gray-300);
        color: var(--gray-500);
        flex: 0 0 auto;
        width: 36px;
        padding: 0.5rem;
    }

    .more-action:hover {
        background: var(--gray-100);
        border-color: var(--gray-400);
        color: var(--gray-700);
    }

    /* Dark theme support for cards */
    [data-theme="dark"] .modern-employee-card {
        background: var(--bg-card);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .modern-employee-card:hover {
        border-color: var(--primary-600);
    }

    [data-theme="dark"] .card-content {
        background: var(--bg-card);
    }

    [data-theme="dark"] .card-avatar {
        border-color: var(--border-light);
    }

    [data-theme="dark"] .name-link {
        color: var(--text-primary);
    }

    [data-theme="dark"] .name-link:hover {
        color: var(--primary-400);
    }

    [data-theme="dark"] .detail-icon {
        color: var(--text-tertiary);
    }

    [data-theme="dark"] .detail-label {
        color: var(--text-secondary);
    }

    [data-theme="dark"] .detail-value {
        color: var(--text-primary);
    }

    [data-theme="dark"] .detail-link {
        color: var(--primary-400);
    }

    [data-theme="dark"] .detail-link:hover {
        color: var(--primary-300);
    }

    [data-theme="dark"] .card-status-section {
        border-color: var(--border-light);
    }

    [data-theme="dark"] .card-actions-section {
        background: var(--bg-secondary);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .card-action-btn {
        background: var(--bg-tertiary);
        color: var(--text-secondary);
        border-color: var(--border-light);
    }

    [data-theme="dark"] .primary-action {
        border-color: var(--primary-600);
        color: var(--primary-400);
    }

    [data-theme="dark"] .primary-action:hover {
        background: var(--bg-quaternary);
        color: var(--primary-300);
    }

    [data-theme="dark"] .more-action:hover {
        background: var(--bg-quaternary);
        color: var(--text-primary);
    }

    /* Responsive card styles */
    @media (max-width: 1200px) {
        .employee-cards-grid {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.25rem;
        }

        .card-content {
            padding: 1.25rem;
        }

        .card-avatar,
        .card-avatar-placeholder {
            width: 56px;
            height: 56px;
        }

        .card-avatar-placeholder {
            font-size: 1.25rem;
        }
    }

    @media (max-width: 992px) {
        .employee-cards-grid {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
        }

        .card-content {
            padding: 1rem;
            gap: 1rem;
        }

        .card-header-section {
            gap: 0.875rem;
        }

        .card-employee-name {
            font-size: 1rem;
        }

        .detail-item {
            gap: 0.625rem;
            padding: 0.375rem 0;
        }

        .card-actions-section {
            padding: 0.875rem 1rem;
        }
    }

    @media (max-width: 768px) {
        .employee-cards-grid {
            grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
            gap: 0.875rem;
        }

        .modern-employee-card {
            border-radius: 8px;
        }

        .card-content {
            padding: 0.875rem;
            gap: 0.875rem;
        }

        .card-header-section {
            gap: 0.75rem;
        }

        .card-avatar,
        .card-avatar-placeholder {
            width: 48px;
            height: 48px;
        }

        .card-avatar-placeholder {
            font-size: 1.125rem;
        }

        .card-employee-name {
            font-size: 0.9375rem;
        }

        .detail-item {
            gap: 0.5rem;
            padding: 0.25rem 0;
        }

        .detail-label {
            font-size: 0.75rem;
        }

        .detail-value {
            font-size: 0.8125rem;
        }

        .card-action-btn {
            padding: 0.375rem 0.625rem;
            font-size: 0.75rem;
            min-height: 32px;
        }

        .card-actions-section {
            padding: 0.75rem 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .employee-cards-grid {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .card-content {
            padding: 0.75rem;
            gap: 0.75rem;
        }

        .card-header-section {
            gap: 0.625rem;
        }

        .card-avatar,
        .card-avatar-placeholder {
            width: 44px;
            height: 44px;
        }

        .card-avatar-placeholder {
            font-size: 1rem;
        }

        .card-employee-name {
            font-size: 0.875rem;
        }

        .id-badge {
            font-size: 0.75rem;
            padding: 0.1875rem 0.375rem;
        }

        .detail-item {
            gap: 0.375rem;
            padding: 0.1875rem 0;
        }

        .detail-icon {
            width: 16px;
            height: 16px;
            font-size: 0.75rem;
        }

        .card-action-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.6875rem;
            min-height: 28px;
            gap: 0.25rem;
        }

        .more-action {
            width: 28px;
            padding: 0.25rem;
        }

        .card-actions-section {
            padding: 0.625rem 0.75rem;
        }
    }
    </style>
            <!-- Enhanced Employee List Container -->
            <div class="employee-list-body" id="employeeListContainer">
                {% if employees %}
                <!-- Enhanced Card View (Hidden by default) -->
                <div id="cardView" class="modern-cards-container" style="display: none;">
                    <div class="employee-cards-grid">
                        {% for employee in employees %}
                        <div class="modern-employee-card"
                             data-emp-id="{{ employee.emp_id }}"
                             data-emp-name="{{ employee.emp_full_name|default:employee.emp_first_name }}"
                             data-dept="{{ employee.department.dept_name|default:'' }}"
                             data-condition="{{ employee.working_condition|default:'' }}">
                            <div class="card-container">
                                <div class="card-content">
                                    <!-- Card Header -->
                                    <div class="card-header-section">
                                        <div class="employee-avatar-section">
                                            {% if employee.emp_image %}
                                            <img src="{{ employee.emp_image|binary_to_img }}"
                                                 alt="{{ employee.emp_full_name }}"
                                                 class="card-avatar">
                                            {% else %}
                                            <div class="card-avatar card-avatar-placeholder">
                                                {{ employee.emp_first_name|slice:":1"|upper }}
                                            </div>
                                            {% endif %}
                                            <div class="status-indicator-wrapper">
                                                {% if employee.working_condition == 'سارى' %}
                                                <span class="status-dot status-active" title="نشط"></span>
                                                {% elif employee.working_condition == 'منقطع عن العمل' %}
                                                <span class="status-dot status-inactive" title="منقطع عن العمل"></span>
                                                {% elif employee.working_condition == 'استقالة' %}
                                                <span class="status-dot status-resigned" title="استقالة"></span>
                                                {% else %}
                                                <span class="status-dot status-unknown" title="غير محدد"></span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="employee-basic-info">
                                            <h6 class="card-employee-name">
                                                <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="name-link">
                                                    {{ employee.emp_full_name|default:employee.emp_first_name }}
                                                </a>
                                            </h6>
                                            <div class="card-employee-id">
                                                <span class="id-badge">
                                                    <i class="fas fa-hashtag"></i>{{ employee.emp_id }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Card Body -->
                                    <div class="card-body-section">
                                        <div class="employee-details-grid">
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-building"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">القسم</span>
                                                    <span class="detail-value">
                                                        {{ employee.department.dept_name|default:"-" }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-briefcase"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">الوظيفة</span>
                                                    <span class="detail-value">
                                                        {{ employee.jop_name|default:"-" }}
                                                    </span>
                                                </div>
                                            </div>
                                            {% if employee.emp_phone1 %}
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-phone"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">الهاتف</span>
                                                    <a href="tel:{{ employee.emp_phone1 }}" class="detail-value detail-link">
                                                        {{ employee.emp_phone1 }}
                                                    </a>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if employee.national_id %}
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-id-card"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">الرقم القومي</span>
                                                    <span class="detail-value">
                                                        {{ employee.national_id }}
                                                    </span>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Card Status -->
                                    <div class="card-status-section">
                                        {% if employee.working_condition == 'سارى' %}
                                        <span class="card-status-badge status-active">
                                            <i class="fas fa-check-circle"></i>نشط
                                        </span>
                                        {% elif employee.working_condition == 'منقطع عن العمل' %}
                                        <span class="card-status-badge status-inactive">
                                            <i class="fas fa-pause-circle"></i>منقطع عن العمل
                                        </span>
                                        {% elif employee.working_condition == 'استقالة' %}
                                        <span class="card-status-badge status-resigned">
                                            <i class="fas fa-times-circle"></i>استقالة
                                        </span>
                                        {% else %}
                                        <span class="card-status-badge status-unknown">
                                            <i class="fas fa-question-circle"></i>{{ employee.working_condition|default:"-" }}
                                        </span>
                                        {% endif %}
                                    </div>

                                    <!-- Card Actions -->
                                    <div class="card-actions-section">
                                        <div class="card-action-buttons">
                                            <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                               class="card-action-btn primary-action" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                                <span>عرض</span>
                                            </a>
                                            {% if perms.Hr.change_employee or user|is_admin %}
                                            <a href="{% url 'Hr:employees:edit' employee.emp_id %}"
                                               class="card-action-btn secondary-action" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                                <span>تعديل</span>
                                            </a>
                                            {% endif %}
                                            <div class="dropdown card-dropdown">
                                                <button type="button" class="card-action-btn more-action dropdown-toggle"
                                                        data-bs-toggle="dropdown" aria-expanded="false" title="المزيد">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu modern-dropdown dropdown-menu-end">
                                                    <li><a class="dropdown-item" href="{% url 'Hr:employees:detail' employee.emp_id %}">
                                                        <i class="fas fa-id-card"></i>البطاقة الشخصية
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#">
                                                        <i class="fas fa-print"></i>طباعة البيانات
                                                    </a></li>
                                                    {% if perms.Hr.delete_employee or user|is_admin %}
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><button class="dropdown-item text-danger delete-employee"
                                                                data-employee-id="{{ employee.emp_id }}"
                                                                data-employee-name="{{ employee.emp_full_name }}">
                                                        <i class="fas fa-trash"></i>حذف
                                                    </button></li>
                                                    {% endif %}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Enhanced Table View -->
                <div id="tableView" class="modern-table-container">
                    <div class="table-wrapper">
                        <table class="modern-employee-table" id="employeesTable">
                            <thead class="modern-table-header">
                                <tr class="header-row">
                                    <th class="modern-table-th sortable-column" data-sort="emp_id">
                                        <div class="column-header">
                                            <div class="column-content">
                                                <i class="fas fa-hashtag column-icon"></i>
                                                <span class="column-title">الرقم</span>
                                            </div>
                                            <div class="sort-indicator">
                                                <i class="fas fa-sort sort-icon"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="modern-table-th sortable-column" data-sort="emp_full_name">
                                        <div class="column-header">
                                            <div class="column-content">
                                                <i class="fas fa-user column-icon"></i>
                                                <span class="column-title">الموظف</span>
                                            </div>
                                            <div class="sort-indicator">
                                                <i class="fas fa-sort sort-icon"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="modern-table-th sortable-column" data-sort="department">
                                        <div class="column-header">
                                            <div class="column-content">
                                                <i class="fas fa-building column-icon"></i>
                                                <span class="column-title">القسم</span>
                                            </div>
                                            <div class="sort-indicator">
                                                <i class="fas fa-sort sort-icon"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="modern-table-th">
                                        <div class="column-header">
                                            <div class="column-content">
                                                <i class="fas fa-briefcase column-icon"></i>
                                                <span class="column-title">الوظيفة</span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="modern-table-th">
                                        <div class="column-header">
                                            <div class="column-content">
                                                <i class="fas fa-phone column-icon"></i>
                                                <span class="column-title">الهاتف</span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="modern-table-th sortable-column" data-sort="working_condition">
                                        <div class="column-header">
                                            <div class="column-content">
                                                <i class="fas fa-user-check column-icon"></i>
                                                <span class="column-title">الحالة</span>
                                            </div>
                                            <div class="sort-indicator">
                                                <i class="fas fa-sort sort-icon"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="modern-table-th actions-column">
                                        <div class="column-header">
                                            <div class="column-content">
                                                <i class="fas fa-cogs column-icon"></i>
                                                <span class="column-title">العمليات</span>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="modern-table-body">
                                {% for employee in employees %}
                                <tr class="modern-table-row employee-row"
                                    data-emp-id="{{ employee.emp_id }}"
                                    data-emp-name="{{ employee.emp_full_name|default:employee.emp_first_name }}"
                                    data-dept="{{ employee.department.dept_name|default:'' }}"
                                    data-condition="{{ employee.working_condition|default:'' }}">

                                    <!-- Employee ID -->
                                    <td class="modern-table-cell id-cell">
                                        <div class="cell-content">
                                            <span class="employee-id-badge">
                                                #{{ employee.emp_id }}
                                            </span>
                                        </div>
                                    </td>

                                    <!-- Employee Info -->
                                    <td class="modern-table-cell employee-info-cell">
                                        <div class="employee-profile">
                                            <div class="employee-avatar-wrapper">
                                                {% if employee.emp_image %}
                                                <img src="{{ employee.emp_image|binary_to_img }}"
                                                     alt="{{ employee.emp_full_name }}"
                                                     class="employee-avatar">
                                                {% else %}
                                                <div class="employee-avatar employee-avatar-placeholder">
                                                    {{ employee.emp_first_name|slice:":1"|upper }}
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="employee-details">
                                                <div class="employee-name-wrapper">
                                                    <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                                       class="employee-name-link">
                                                        {{ employee.emp_full_name|default:employee.emp_first_name }}
                                                    </a>
                                                </div>
                                                {% if employee.national_id %}
                                                <div class="employee-meta-info">
                                                    <span class="meta-item">
                                                        <i class="fas fa-id-card meta-icon"></i>
                                                        <span class="meta-text">{{ employee.national_id }}</span>
                                                    </span>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Department -->
                                    <td class="modern-table-cell department-cell">
                                        <div class="cell-content">
                                            {% if employee.department %}
                                            <span class="department-badge">
                                                <i class="fas fa-building badge-icon"></i>
                                                <span class="badge-text">{{ employee.department.dept_name }}</span>
                                            </span>
                                            {% else %}
                                            <span class="empty-value">-</span>
                                            {% endif %}
                                        </div>
                                    </td>

                                    <!-- Job -->
                                    <td class="modern-table-cell job-cell">
                                        <div class="cell-content">
                                            {% if employee.jop_name %}
                                            <span class="job-title">{{ employee.jop_name }}</span>
                                            {% else %}
                                            <span class="empty-value">-</span>
                                            {% endif %}
                                        </div>
                                    </td>

                                    <!-- Phone -->
                                    <td class="modern-table-cell phone-cell">
                                        <div class="cell-content">
                                            {% if employee.emp_phone1 %}
                                            <a href="tel:{{ employee.emp_phone1 }}" class="phone-link">
                                                <i class="fas fa-phone phone-icon"></i>
                                                <span class="phone-number">{{ employee.emp_phone1 }}</span>
                                            </a>
                                            {% else %}
                                            <span class="empty-value">-</span>
                                            {% endif %}
                                        </div>
                                    </td>

                                    <!-- Status -->
                                    <td class="modern-table-cell status-cell">
                                        <div class="cell-content">
                                            {% if employee.working_condition == 'سارى' %}
                                            <span class="status-badge status-active">
                                                <i class="fas fa-check-circle status-icon"></i>
                                                <span class="status-text">نشط</span>
                                            </span>
                                            {% elif employee.working_condition == 'منقطع عن العمل' %}
                                            <span class="status-badge status-inactive">
                                                <i class="fas fa-pause-circle status-icon"></i>
                                                <span class="status-text">منقطع</span>
                                            </span>
                                            {% elif employee.working_condition == 'استقالة' %}
                                            <span class="status-badge status-resigned">
                                                <i class="fas fa-times-circle status-icon"></i>
                                                <span class="status-text">استقالة</span>
                                            </span>
                                            {% else %}
                                            <span class="status-badge status-unknown">
                                                <i class="fas fa-question-circle status-icon"></i>
                                                <span class="status-text">{{ employee.working_condition|default:"غير محدد" }}</span>
                                            </span>
                                            {% endif %}
                                        </div>
                                    </td>

                                    <!-- Actions -->
                                    <td class="modern-table-cell actions-cell">
                                        <div class="cell-content">
                                            <div class="action-buttons">
                                                <div class="primary-actions">
                                                    <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                                       class="action-btn view-btn" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if perms.Hr.change_employee or user|is_admin %}
                                                    <a href="{% url 'Hr:employees:edit' employee.emp_id %}"
                                                       class="action-btn edit-btn" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    {% endif %}
                                                </div>
                                                <div class="dropdown action-dropdown">
                                                    <button type="button" class="action-btn more-btn dropdown-toggle"
                                                            data-bs-toggle="dropdown" aria-expanded="false" title="المزيد">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu modern-dropdown dropdown-menu-end">
                                                        <li><h6 class="dropdown-header">إجراءات الموظف</h6></li>
                                                        <li><a class="dropdown-item" href="{% url 'Hr:employees:detail' employee.emp_id %}">
                                                            <i class="fas fa-id-card"></i>البطاقة الشخصية
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#">
                                                            <i class="fas fa-print"></i>طباعة البيانات
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#">
                                                            <i class="fas fa-file-export"></i>تصدير البيانات
                                                        </a></li>
                                                        {% if perms.Hr.delete_employee or user|is_admin %}
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><button class="dropdown-item text-danger delete-employee"
                                                                    data-employee-id="{{ employee.emp_id }}"
                                                                    data-employee-name="{{ employee.emp_full_name }}">
                                                            <i class="fas fa-trash"></i>حذف الموظف
                                                        </button></li>
                                                        {% endif %}
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

        {% else %}
        <div class="empty-state">
            <div class="empty-state-content">
                <div class="empty-state-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="empty-state-title">
                    {% if status == 'inactive' %}
                    لا توجد موظفين غير نشطين
                    {% else %}
                    لا توجد موظفين نشطين
                    {% endif %}
                </div>
                <div class="empty-state-description">
                    {% if request.GET %}
                    لم يتم العثور على موظفين يطابقون معايير البحث المحددة.
                    جرب تعديل الفلاتر أو البحث بكلمات مختلفة.
                    {% else %}
                    {% if status == 'inactive' %}
                    لا يوجد موظفين غير نشطين في النظام حالياً.
                    {% else %}
                    لا يوجد موظفين نشطين في النظام حالياً.
                    {% endif %}
                    {% endif %}
                </div>
                <div class="empty-state-actions">
                    {% if request.GET %}
                    <a href="{% url 'Hr:employees:list' %}" class="btn btn-outline-primary btn-with-icon">
                        <i class="fas fa-undo"></i>
                        <span>إعادة تعيين الفلاتر</span>
                    </a>
                    {% endif %}
                    {% if perms.Hr.add_employee or user|is_admin %}
                    <a href="{% url 'Hr:employees:create' %}" class="btn btn-primary btn-with-icon">
                        <i class="fas fa-user-plus"></i>
                        <span>إضافة موظف جديد</span>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
            </div>
        </div>

    {% if employees_by_department %}
    <div class="section-spacing">
        <div class="card">
            <div class="card-header">
                <div class="card-header-content">
                    <div class="card-title">
                        <i class="fas fa-chart-pie"></i>
                        <span>توزيع الموظفين حسب الأقسام</span>
                    </div>
                    <div class="card-subtitle">عرض إحصائيات الموظفين موزعة على الأقسام المختلفة</div>
                </div>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {% for dept in employees_by_department %}
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h6 class="text-sm font-medium text-muted">{{ dept.dept_name }}</h6>
                                {% if dept.dept_code %}
                                <a href="{% url 'Hr:departments:department_detail' dept.dept_code %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% endif %}
                            </div>
                            <div class="text-2xl font-bold text-primary mb-2">{{ dept.count }}</div>
                            <div class="progress-container mb-2">
                                <div class="progress-bar bg-primary"
                                     style="width: {% widthratio dept.count total_employees 100 %}%;">
                                </div>
                            </div>
                            <small class="text-muted">{% widthratio dept.count total_employees 100 %}% من الإجمالي</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
        </div>
    </div>

    <!-- Quick Links Section -->
    <div class="section-spacing">
        <div class="card">
            <div class="card-header">
                <div class="card-header-content">
                    <div class="card-title">
                        <i class="fas fa-link"></i>
                        <span>روابط سريعة</span>
                    </div>
                    <div class="card-subtitle">الوصول السريع للعمليات الأكثر استخداماً</div>
                </div>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="{% url 'Hr:employees:list' %}" class="card card-hover">
                        <div class="card-body text-center">
                            <div class="w-12 h-12 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-users text-primary text-xl"></i>
                            </div>
                            <h6 class="font-medium mb-2">قائمة الموظفين</h6>
                            <p class="text-sm text-muted">عرض وإدارة بيانات الموظفين</p>
                        </div>
                    </a>

                    <a href="{% url 'Hr:employees:create' %}" class="card card-hover">
                        <div class="card-body text-center">
                            <div class="w-12 h-12 bg-success-light rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-user-plus text-success text-xl"></i>
                            </div>
                            <h6 class="font-medium mb-2">إضافة موظف جديد</h6>
                            <p class="text-sm text-muted">تسجيل بيانات موظف جديد</p>
                        </div>
                    </a>

                    <a href="{% url 'Hr:departments:department_list' %}" class="card card-hover">
                        <div class="card-body text-center">
                            <div class="w-12 h-12 bg-info-light rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-building text-info text-xl"></i>
                            </div>
                            <h6 class="font-medium mb-2">الأقسام</h6>
                            <p class="text-sm text-muted">عرض وإدارة الأقسام</p>
                        </div>
                    </a>

                    <a href="{% url 'Hr:jobs:job_list' %}" class="card card-hover">
                        <div class="card-body text-center">
                            <div class="w-12 h-12 bg-warning-light rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-briefcase text-warning text-xl"></i>
                            </div>
                            <h6 class="font-medium mb-2">الوظائف</h6>
                            <p class="text-sm text-muted">عرض وإدارة المسميات الوظيفية</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notifications -->
<div class="toast-container">
    <div id="successToast" class="toast toast-success" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-content">
            <div class="toast-body">
                <i class="fas fa-check-circle"></i>
                <span class="toast-message"></span>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<!-- Loading Indicator -->
<div id="loadingIndicator" class="loading-indicator" style="display: none;">
    <div class="progress-bar">
        <div class="progress-bar-fill"></div>
    </div>
</div>

<!-- Quick Actions Button -->
<div class="quick-actions-fab">
    <button id="quickActionsBtn" type="button" class="fab-button">
        <i class="fas fa-bolt"></i>
    </button>
</div>

<!-- Quick Actions Menu -->
<div class="quick-actions-menu" style="display: none;">
    <h6 class="menu-title">إجراءات سريعة</h6>
    <div class="menu-items">
        <a href="{% url 'Hr:employees:create' %}" class="menu-item">
            <i class="fas fa-user-plus"></i>إضافة موظف جديد
        </a>
        <button class="menu-item" onclick="exportToExcel()">
            <i class="fas fa-file-excel"></i>تصدير إلى Excel
        </button>
        <button class="menu-item" onclick="printEmployeeList()">
            <i class="fas fa-print"></i>طباعة القائمة
        </button>
    </div>
</div>

<!-- Keyboard Shortcuts Guide -->
<div class="modal" id="keyboardShortcutsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-keyboard"></i>
                    اختصارات لوحة المفاتيح
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="shortcuts-grid">
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Ctrl</kbd> + <kbd>F</kbd>
                        </div>
                        <span class="shortcut-description">بحث سريع</span>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Ctrl</kbd> + <kbd>N</kbd>
                        </div>
                        <span class="shortcut-description">موظف جديد</span>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Ctrl</kbd> + <kbd>P</kbd>
                        </div>
                        <span class="shortcut-description">طباعة القائمة</span>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Ctrl</kbd> + <kbd>E</kbd>
                        </div>
                        <span class="shortcut-description">تصدير البيانات</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- Enhanced JavaScript for Employee List -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View Toggle Functionality
    const tableViewBtn = document.getElementById('tableViewBtn');
    const cardViewBtn = document.getElementById('cardViewBtn');
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');

    // Initialize view state
    let currentView = 'table'; // Default to table view

    // View toggle function
    function toggleView(viewType) {
        if (viewType === 'table') {
            // Show table view
            tableView.style.display = 'block';
            cardView.style.display = 'none';

            // Update button states
            tableViewBtn.classList.add('view-toggle-active');
            cardViewBtn.classList.remove('view-toggle-active');

            // Update icon in header
            const viewToggleIcon = document.querySelector('#viewToggle i');
            if (viewToggleIcon) {
                viewToggleIcon.className = 'fas fa-th-large';
            }

            currentView = 'table';
        } else if (viewType === 'card') {
            // Show card view
            tableView.style.display = 'none';
            cardView.style.display = 'block';

            // Update button states
            cardViewBtn.classList.add('view-toggle-active');
            tableViewBtn.classList.remove('view-toggle-active');

            // Update icon in header
            const viewToggleIcon = document.querySelector('#viewToggle i');
            if (viewToggleIcon) {
                viewToggleIcon.className = 'fas fa-table';
            }

            currentView = 'card';
        }

        // Store preference in localStorage
        localStorage.setItem('employeeViewPreference', currentView);

        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('viewChanged', {
            detail: { view: currentView }
        }));
    }

    // Event listeners for view toggle buttons
    if (tableViewBtn) {
        tableViewBtn.addEventListener('click', function() {
            toggleView('table');
        });
    }

    if (cardViewBtn) {
        cardViewBtn.addEventListener('click', function() {
            toggleView('card');
        });
    }

    // Header view toggle button
    const headerViewToggle = document.getElementById('viewToggle');
    if (headerViewToggle) {
        headerViewToggle.addEventListener('click', function() {
            const nextView = currentView === 'table' ? 'card' : 'table';
            toggleView(nextView);
        });
    }

    // Load saved view preference
    const savedView = localStorage.getItem('employeeViewPreference');
    if (savedView && (savedView === 'table' || savedView === 'card')) {
        toggleView(savedView);
    }

    // Active/Inactive Employee Status Toggle
    const activeBtn = document.getElementById('activeEmployeesBtn');
    const inactiveBtn = document.getElementById('inactiveEmployeesBtn');
    const pageTitle = document.querySelector('.page-title h1');
    const statsCards = document.querySelectorAll('.stat-card');

    let currentStatus = 'active'; // Default to active employees

    // Status toggle function
    function toggleEmployeeStatus(status) {
        if (status === 'active') {
            // Update button states
            if (activeBtn) {
                activeBtn.classList.add('status-btn-active');
                activeBtn.classList.remove('status-btn-inactive');
            }
            if (inactiveBtn) {
                inactiveBtn.classList.remove('status-btn-active');
                inactiveBtn.classList.add('status-btn-inactive');
            }

            // Update page title
            if (pageTitle) {
                pageTitle.textContent = 'الموظفين النشطين';
            }

            currentStatus = 'active';
        } else if (status === 'inactive') {
            // Update button states
            if (inactiveBtn) {
                inactiveBtn.classList.add('status-btn-active');
                inactiveBtn.classList.remove('status-btn-inactive');
            }
            if (activeBtn) {
                activeBtn.classList.remove('status-btn-active');
                activeBtn.classList.add('status-btn-inactive');
            }

            // Update page title
            if (pageTitle) {
                pageTitle.textContent = 'الموظفين غير النشطين';
            }

            currentStatus = 'inactive';
        }

        // Show loading state
        showLoadingState();

        // Filter employees based on status
        filterEmployeesByStatus(status);

        // Store preference
        localStorage.setItem('employeeStatusFilter', currentStatus);

        // Trigger custom event
        window.dispatchEvent(new CustomEvent('statusFilterChanged', {
            detail: { status: currentStatus }
        }));
    }

    // Event listeners for status toggle buttons
    if (activeBtn) {
        activeBtn.addEventListener('click', function() {
            if (currentStatus !== 'active') {
                toggleEmployeeStatus('active');
            }
        });
    }

    if (inactiveBtn) {
        inactiveBtn.addEventListener('click', function() {
            if (currentStatus !== 'inactive') {
                toggleEmployeeStatus('inactive');
            }
        });
    }

    // Filter employees by status
    function filterEmployeesByStatus(status) {
        const tableRows = document.querySelectorAll('.modern-table-row');
        const employeeCards = document.querySelectorAll('.modern-employee-card');

        // Filter table rows
        tableRows.forEach(row => {
            const empCondition = row.dataset.condition || '';
            const isActive = empCondition === 'سارى';

            if (status === 'active') {
                row.style.display = isActive ? '' : 'none';
            } else {
                row.style.display = !isActive ? '' : 'none';
            }
        });

        // Filter employee cards
        employeeCards.forEach(card => {
            const empCondition = card.dataset.condition || '';
            const isActive = empCondition === 'سارى';

            if (status === 'active') {
                card.style.display = isActive ? '' : 'none';
            } else {
                card.style.display = !isActive ? '' : 'none';
            }
        });

        // Update statistics
        updateStatistics(status);

        // Hide loading state
        hideLoadingState();
    }

    // Show loading state
    function showLoadingState() {
        const container = document.getElementById('employeeListContainer');
        if (container) {
            container.style.opacity = '0.6';
            container.style.pointerEvents = 'none';
        }
    }

    // Hide loading state
    function hideLoadingState() {
        const container = document.getElementById('employeeListContainer');
        if (container) {
            container.style.opacity = '1';
            container.style.pointerEvents = 'auto';
        }
    }

    // Update statistics based on filtered employees
    function updateStatistics(status) {
        const tableRows = document.querySelectorAll('.modern-table-row');
        const visibleRows = Array.from(tableRows).filter(row => row.style.display !== 'none');

        // Update total count in stats
        const totalStat = document.querySelector('.stat-card .stat-number');
        if (totalStat) {
            totalStat.textContent = visibleRows.length;
        }

        // Update other statistics as needed
        // This would typically involve more complex calculations
    }

    // Load saved status filter
    const savedStatus = localStorage.getItem('employeeStatusFilter');
    if (savedStatus && (savedStatus === 'active' || savedStatus === 'inactive')) {
        toggleEmployeeStatus(savedStatus);
    }

    // Enhanced search functionality
    const searchInput = document.getElementById('employeeSearch');
    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(this.value.trim());
            }, 300);
        });
    }

    // Perform search
    function performSearch(query) {
        const tableRows = document.querySelectorAll('.modern-table-row');
        const employeeCards = document.querySelectorAll('.modern-employee-card');

        if (!query) {
            // Show all employees based on current status filter
            filterEmployeesByStatus(currentStatus);
            return;
        }

        const searchTerm = query.toLowerCase();

        // Search in table rows
        tableRows.forEach(row => {
            const empName = (row.dataset.empName || '').toLowerCase();
            const empId = (row.dataset.empId || '').toLowerCase();
            const dept = (row.dataset.dept || '').toLowerCase();

            const matches = empName.includes(searchTerm) ||
                          empId.includes(searchTerm) ||
                          dept.includes(searchTerm);

            // Apply both search and status filters
            const empCondition = row.dataset.condition || '';
            const isActive = empCondition === 'سارى';
            const statusMatch = (currentStatus === 'active' && isActive) ||
                              (currentStatus === 'inactive' && !isActive);

            row.style.display = (matches && statusMatch) ? '' : 'none';
        });

        // Search in employee cards
        employeeCards.forEach(card => {
            const empName = (card.dataset.empName || '').toLowerCase();
            const empId = (card.dataset.empId || '').toLowerCase();
            const dept = (card.dataset.dept || '').toLowerCase();

            const matches = empName.includes(searchTerm) ||
                          empId.includes(searchTerm) ||
                          dept.includes(searchTerm);

            // Apply both search and status filters
            const empCondition = card.dataset.condition || '';
            const isActive = empCondition === 'سارى';
            const statusMatch = (currentStatus === 'active' && isActive) ||
                              (currentStatus === 'inactive' && !isActive);

            card.style.display = (matches && statusMatch) ? '' : 'none';
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + T for table view
        if ((e.ctrlKey || e.metaKey) && e.key === 't') {
            e.preventDefault();
            toggleView('table');
        }

        // Ctrl/Cmd + G for card view
        if ((e.ctrlKey || e.metaKey) && e.key === 'g') {
            e.preventDefault();
            toggleView('card');
        }

        // Ctrl/Cmd + F for search focus
        if ((e.ctrlKey || e.metaKey) && e.key === 'f' && searchInput) {
            e.preventDefault();
            searchInput.focus();
        }
    });

    // Initialize smooth transitions
    if (tableView) {
        tableView.style.transition = 'opacity 0.3s ease-in-out';
    }
    if (cardView) {
        cardView.style.transition = 'opacity 0.3s ease-in-out';
    }
});
</script>

{% endblock %}
